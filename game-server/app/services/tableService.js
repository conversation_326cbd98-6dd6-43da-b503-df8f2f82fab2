var events      = require('events');
var uuid        = require('node-uuid');
var logger      = require('pomelo-logger').getLogger('game-log', __filename);
var TableStore  = require('../../app/persistence/tables');
var logsGameDao = require('../dao/logsGameDao');

var UserStore   = require('../../app/persistence/users');
var userDao     = require('../dao/userDao');

var dispatcher  = require('../util/dispatcher');
var Table       = require('../game/table');

var _           = require('underscore');
var schedule    = require('pomelo-scheduler');
var consts      = require('../consts/consts');
var CODE        = require('../consts/code');

var rabbitConn  = require('../dao/rabbitmq/rabbitmq');
var utils       = require('../util/utils');
// var rooms       = require('../../config/data/room');
var rooms5      = require('../../config/data/rooms5');
var rooms9      = require('../../config/data/rooms9');

// const roomsA5     = require('../../config/data/roomsA5.json');
// const roomsA9     = require('../../config/data/roomsA9.json');
// const roomsB5     = require('../../config/data/roomsB5.json');
// const roomsB9     = require('../../config/data/roomsB9.json');
// const roomsC5     = require('../../config/data/roomsC5.json');
// const roomsC9     = require('../../config/data/roomsC9.json');

var async       = require('async');
const ZONES     = require('../consts/zones');
const pomelo    = require('pomelo');


/**
 * Create and maintain table tables.
 *
 * TableService is created by tableComponent.
 *
 * @class
 * @constructor
 */
var TableService = function(app, opts) {
    opts = opts || {};
    this.app = app;
    this.tables = {};
    this.prefix = opts.prefix;
    this.store = opts.store;
    this.stateService = this.app.get('stateService');
    //this.sessionService = this.app.get('sessionService');
    this.seatList = [];   //{seatNr:xx, uid:xx} => added

    // add list scheduleJob
    this.jobQueue   = [];

    this.currentDisplayTid = 1; // Biến để theo dõi giá trị display_tid hiện tại

    //this.rabbitConn = this.app.get('rabbitConn');

    //this.init();

};

// init
TableService.prototype.init = function () {
    logger.info("---> khoi tao class TableService");
    for (var i = 1; i <= 9; i++) {
        this.seatList.push({seatNr: i, uid: -100});
    }
    // logger.info("--> seatList: " + JSON.stringify(this.seatList));
};


module.exports = TableService;


/**
 * Get the player object in JSON format
 * @param tid
 * @param seatNr
 * @returns {*} player object
 */
TableService.prototype.autoInitTablesPerZone = function () {

    const initRooms = (rooms) => {
        for (let i = 0; i < rooms.length; i++) {
            this.createTable(i, {
                "smallBlind": rooms[i].smallBlind,
                "bigBlind": rooms[i].bigBlind,
                "minBuyIn": rooms[i].minBuyIn,
                "maxBuyIn": rooms[i].maxBuyIn,
                "minPlayers": rooms[i].minPlayers,
                "maxPlayers": rooms[i].maxPlayers,
                "gameMode": rooms[i].gameMode,
                "zone": rooms[i].zone,
            }, (err, tid) => {
                logger.info(`Zone ${rooms[i].zone} - Room ${i} -> autoInitTablesPerZone >> create table with tid: ${tid}`);
            });
        }
    };

    // Init rooms5 and rooms9
    initRooms(rooms5);
    initRooms(rooms9);

    // const zones = [
    //     { rooms: roomsA5.concat(roomsA9), zone: 'TS' },
    //     { rooms: roomsB5.concat(roomsB9), zone: 'TC' },
    //     { rooms: roomsC5.concat(roomsC9), zone: 'CC' }
    // ];

    // zones.forEach((zoneData, zoneIndex) => {
    //     zoneData.rooms.forEach((room, roomIndex) => {
    //         this.createTable(roomIndex, {
    //             "smallBlind": room.smallBlind,
    //             "bigBlind": room.bigBlind,
    //             "minBuyIn": room.minBuyIn,
    //             "maxBuyIn": room.maxBuyIn,
    //             "minPlayers": room.minPlayers,
    //             "maxPlayers": room.maxPlayers,
    //             "gameMode": room.gameMode,
    //             "zone": zoneData.zone,
    //         }, (err, tid) => {
    //             logger.info(`Zone ${zoneIndex} - Room ${roomIndex} -> autoInitTablesPerZone >> create table with tid: ${tid}`);
    //         });
    //     });
    // });
}

// TableService.prototype.autoInitTablesPerZone = function () {
//     // danh sách rooms mặc định từ file config
//     let listRoom = rooms;
//     // loop Zone
//     for (var zone in ZONES) {
//         for (var i = 1; i <= 10; i++) {
//             // 5 bàn đầu tiên là bàn 5
//             if (i <= 5)  {
//                 listRoom = rooms5;
//             } else {
//                 listRoom == rooms9;
//             }
//             // 5 bàn sau là bàn 9
//             var objMakeTable = {
//                 "smallBlind": listRoom[i].smallBlind,
//                 "bigBlind": listRoom[i].bigBlind,
//                 "minBuyIn": listRoom[i].minBuyIn,
//                 "maxBuyIn": listRoom[i].maxBuyIn,
//                 "minPlayers": listRoom[i].minPlayers,
//                 "maxPlayers": listRoom[i].maxPlayers,
//                 "gameMode": listRoom[i].gameMode,
//                 "zone": zone,
//             };
//             this.createTable(i, objMakeTable, (err, tid) => {
//                 logger.info("autoInitTablesPerZone >> create table with tid: ", tid);
//             });
//         }
//     }
// }

TableService.prototype.start = function(cb){
    cb();
};

TableService.prototype.stop = function(force, cb){
    cb();
};

TableService.prototype.getTable = function(tid){
    return this.tables[tid];
};

TableService.prototype.getTableDetail = function (tid) {
    var table = this.tables[tid];
    var arrRes = {
        status: false,
        data: []
    };
    if (table) {
        var tb = {
            id         : table.id,
            zone       : table.zone,
            smallBlind : table.table.smallBlind,
            bigBlind   : table.table.bigBlind,
            minBuyIn   : table.table.minBuyIn,
            maxBuyIn   : table.table.maxBuyIn,
            minPlayers : table.table.minPlayers,
            maxPlayers : table.table.maxPlayers,
            gameMode   : table.table.gameMode
        };
        arrRes = {
            status: true,
            data: tb
        }
    }

    return arrRes;
};


/**
 * Return a list of tables
 *
 * @param {string} [zone] the zone to filter tables by
 * @return {Object} tables
 * @prop {Array} tables an array of tables
 * @prop {number} totalMembers total number of members in all tables
 * @prop {number} totalPlayers total number of players in all tables
 * @prop {Object} tables.table an object with details about the table
 * @prop {number} tables.table.id table id
 * @prop {string} tables.table.displayTid display table id
 * @prop {string} tables.table.zone table zone
 * @prop {number} tables.table.smallBlind small blind
 * @prop {number} tables.table.bigBlind big blind
 * @prop {number} tables.table.minBuyIn minimum buy in
 * @prop {number} tables.table.maxBuyIn maximum buy in
 * @prop {number} tables.table.minPlayers minimum number of players
 * @prop {number} tables.table.maxPlayers maximum number of players
 * @prop {string} tables.table.gameMode game mode
 * @prop {number} tables.table.players number of players currently playing
 * @prop {number} tables.table.members number of members currently in the table
 */
TableService.prototype.getTables = function(zone = null) {
    var tables = {
        tables       : [],
        totalMembers : 0,
        totalPlayers : 0
    };
    for(var i in this.tables){
        var table = this.tables[i];
        if (zone && table.zone !== zone) {
            continue; // skip tables that don't belong to the specified zone
        }
        var members = table.table.members.length;
        var players = (table.table.players.length - table.table.playersToRemove.length);
        tables.totalMembers += members;
        tables.totalPlayers += players;
        tables.tables.push({
            id         : table.id,
            displayTid : table.display_tid,
            zone       : table.zone,
            smallBlind : table.table.smallBlind,
            bigBlind   : table.table.bigBlind,
            minBuyIn   : table.table.minBuyIn,
            maxBuyIn   : table.table.maxBuyIn,
            minPlayers : table.table.minPlayers,
            maxPlayers : table.table.maxPlayers,
            gameMode   : table.table.gameMode,
            players    : players,
            members    : members
        });
    }
    return tables;
};

/**
 *
 * @param uid
 * @param obj
 * @param cb
 * @return {*}
 */
TableService.prototype.createTable = function(uid, obj, cb) {

    this.init();

    if(!obj || (obj && (
            isNaN(obj.smallBlind) ||
            isNaN(obj.bigBlind)   ||
            isNaN(obj.minBuyIn)   ||
            isNaN(obj.maxBuyIn)   ||
            isNaN(obj.minPlayers) ||
            isNaN(obj.maxPlayers) ||
            obj.minPlayers < 2    ||
            obj.minPlayers > 10   ||
            obj.maxPlayers < 2    ||
            obj.maxPlayers > 10
        ))){
        return cb('invalid-table-rules');
    }
    var tid = uuid.v1();
    this.tables[tid] = {};
    this.tables[tid].id = tid;
    this.tables[tid].creator = uid;
    this.tables[tid].state = 'JOIN';
    this.tables[tid].zone = ZONES[obj?.zone ?? 'TS'];
    this.tables[tid].display_tid = this.currentDisplayTid++; // Gán giá trị display_tid và tăng giá trị hiện tại
    this.tables[tid].tableService = this;
    obj.smallBlind = Math.round(parseInt(obj.smallBlind));
    obj.bigBlind = Math.round(parseInt(obj.bigBlind));
    obj.minBuyIn = Math.round(parseInt(obj.minBuyIn));
    obj.maxBuyIn = Math.round(parseInt(obj.maxBuyIn));
    obj.minPlayers = Math.round(parseInt(obj.minPlayers));
    obj.maxPlayers = Math.round(parseInt(obj.maxPlayers));
    obj.gameMode = (obj.gameMode == 'normal' || obj.gameMode == 'fast') ? obj.gameMode : 'normal';
    this.tables[tid].table = new Table(obj.smallBlind, obj.bigBlind, obj.minPlayers, obj.maxPlayers, obj.minBuyIn, obj.maxBuyIn, obj.gameMode, this.tables[tid]);
    // automatically join created table
//        session.set('tid', table.id);
//        var tid = session.get('tid');
//        me.app.rpc.chat.chatRemote.add(session, session.uid, tid, function(e, users){
//            if(e){
//                next(500, {
//                    code  : 200,
//                    error : e
//                });
//                return;
//            }
//            var channelService = me.app.get('channelService');
//            var channel = channelService.getChannel(tid, true);
//            channel.pushMessage({
//                route  : 'onTableEvent',
//                msg    : tableService.getTableJSON(tid, session.uid)
//            });
//            channel.pushMessage({
//                route : 'onUpdateUsers',
//                users : users
//            });
//            tableService.broadcastGameState(tid);
//            next(null, {
//                code  : 200,
//                route : msg.route
//            });
//        });
    //logger.info(" in tableService: ", this.tables[tid].id);
    //cb(null, this.tables[tid]);
    //this.tables[tid].table.eventEmitter.emit('newTable');

    logger.info("[createTable] >> create table with tid: ", tid);
    // logger.info("[createTable] >> create table with table: ", this.tables[tid]);
    cb(null, this.tables[tid].id);
};

/**
 * [Không sử dụng]
 * - Hàm tạo bàn + tự động join bàn sau khi tạo xong
 *
 * @param uid
 * @param obj
 * @param cb
 * @return {*}
 */
//TableService.prototype.createAndJoinTable = function(uid, obj, cb){
TableService.prototype.createAndJoinTable = function(session, uid, obj, cb){

    this.init();

    if(!obj || (obj && (
            isNaN(obj.smallBlind) ||
            isNaN(obj.bigBlind)   ||
            isNaN(obj.minBuyIn)   ||
            isNaN(obj.maxBuyIn)   ||
            isNaN(obj.minPlayers) ||
            isNaN(obj.maxPlayers) ||
            obj.minPlayers < 2    ||
            obj.minPlayers > 10   ||
            obj.maxPlayers < 2    ||
            obj.maxPlayers > 10
        ))){
        return cb('invalid-table-rules');
    }
    var tid = uuid.v1();
    this.tables[tid] = {};
    this.tables[tid].id = tid;
    this.tables[tid].creator = uid;
    this.tables[tid].state = 'JOIN';

    this.tables[tid].zone = ZONES[obj?.zone ?? 'TS'];
    this.tables[tid].display_tid = this.currentDisplayTid++; // Gán giá trị display_tid và tăng giá trị hiện tại

    this.tables[tid].tableService = this;
    obj.smallBlind = Math.round(parseInt(obj.smallBlind));
    obj.bigBlind = Math.round(parseInt(obj.bigBlind));
    obj.minBuyIn = Math.round(parseInt(obj.minBuyIn));
    obj.maxBuyIn = Math.round(parseInt(obj.maxBuyIn));
    obj.minPlayers = Math.round(parseInt(obj.minPlayers));
    obj.maxPlayers = Math.round(parseInt(obj.maxPlayers));
    obj.gameMode = (obj.gameMode == 'normal' || obj.gameMode == 'fast') ? obj.gameMode : 'normal';
    this.tables[tid].table = new Table(obj.smallBlind, obj.bigBlind, obj.minPlayers, obj.maxPlayers, obj.minBuyIn, obj.maxBuyIn, obj.gameMode, this.tables[tid]);

    // automatically join created table
    var me = this;
    var tableService = this.app.get('tableService');
    var table = tableService.getTable(tid);
    //if(!table.tid || !table){
    if(!table.id){
        cb(null, {
            code  : 500,
            error : 'invalid-table'
        });
        return;
    }

    session.set('tid', table.id);
    //var tid = session.get('tid');

    me.app.rpc.chat.chatRemote.add(session, session.uid, tid, function(e, users) {

        if(e){
            cb(500, {
                code  : 200,
                error : e
            });
            return;
        }
        /*
        var channelService = me.app.get('channelService');
        var channel = channelService.getChannel(tid, true);
        channel.pushMessage({
            route  : 'onTableEvent',
            msg    : tableService.getTableJSON(tid, session.uid)
        });
        channel.pushMessage({
            route : 'onUpdateUsers',
            users : users
        });
        tableService.broadcastGameState(tid);
        cb(null, {
            code  : 200,
            route : msg.route
        });
        */

        tableService.addMember(tid, session.uid, function(e){
            cb(null, {
                code  : 200,
                route : msg.route,
                msg: e // tra ve danh sach nguoi choi đã ngồi xuống bàn
            });
        });

    });

    cb(null, this.tables[tid]);
};

/**
 * Lấy thông tin table tương ứng với số tiền để tạo bàn
 * @param balance
 */
TableService.prototype.getInfoTableCreate = function (balance, maxPlayers) {
    //var current_balance = user.balance;
    // var listRoom = rooms;
    var listRoom = rooms5;
    if (maxPlayers == 5){
        listRoom = rooms5;
    }else if(maxPlayers == 9){
        listRoom == rooms9;
    }

    logger.info("getInfoTableCreate >> listRoom: ", listRoom);
    var _arrTmp = _.filter(listRoom, function (num) {
        //return balance >= num.maxBuyIn;
        return balance >= num.maxBuyIn && maxPlayers == num.maxPlayers;

    });

    var _arrSort        = _.sortBy(_arrTmp, 'maxBuyIn');
    var _arrSortReverse = _arrSort.reverse();
    var _tblInfo        = _arrSortReverse[0];
    logger.info("getInfoTableCreate >> balance: ", balance, " => maxPlayers: ", maxPlayers);
    logger.info("getInfoTableCreate >> _arrTmp: ", _arrTmp);
    logger.info("getInfoTableCreate >> _tblInfo: ", _tblInfo);

    var tblInfoCreate = _.pick(_tblInfo, 'smallBlind', 'bigBlind', 'minBuyIn', 'maxBuyIn', 'minPlayers', 'maxPlayers', 'gameMode');
    return tblInfoCreate;
};

/**
 * Tìm bàn tương ứng với số tiền
 * @param session
 * @param user
 * @param cb
 */
TableService.prototype.findNewTables = function (session, user, tableInfo, cb) {

    var me = this;
    var tableService = this.app.get('tableService');
    // const zone = tableInfo?.zone ?? null;
    // Lấy danh sách bàn chơi -----------------------------------------------------
    //var _tblList = tableService.getTables();
    var _tblList = me.getTables();
    // fake data table list = 0
    // _tblList = {tables: []};

    // TH không có bàn chơi nào thì tạo bàn mới
    if (_tblList.tables.length == 0) {

        // set lai session tid = undefined
        /*
        session.set('tid', undefined);
        // Begin: tạo bàn mới --------------------------------------------
        if (session.get('tid')) {
            cb(null, {
                code: CODE.FAIL,
                error: 'already-in-table'
            });
        }
        */

        let tblInfoCreate = me.getInfoTableCreate(user.balance, tableInfo.maxPlayers);
        // tblInfoCreate.zone = zone;
        logger.info("[findNewTables] Thông tin table định tạo: ", tblInfoCreate);

        var dataCb = {};

        // me.createTable(session.uid, tblInfoCreate, function (e) {
        me.createTable(session.uid, tblInfoCreate, function (e, tid) {
            logger.info("[findNewTables] createTable e: ", e, ' -> tid: ', tid);
            if (e) {
                dataCb = {code:CODE.FAIL, data: e, msg: "can not create table!"};
                cb(dataCb);
                return;
            }

            dataCb = {
                code: CODE.OK,
                data: tblInfoCreate, // trả về thông tin bàn chơi
                tid: tid // e // trả về id của tables sẽ được vào
            };

            cb(dataCb);
            return;
        });

        // end if truong _tablesTotal == 0
    } else {

        /**
         * TH đã có danh sách bàn chơi
         * - lại check trong danh sách bàn chơi với số tiền mình đang có, xem có bàn chơi nào đúng rule đưa ra
         */
        var current_balance = user.balance;

        // Tìm những bàn mà số tiền của mình > maxBuyIn của bàn tương ứng
        var _arrTable = _.filter(_tblList.tables, function (num) {
            //return current_balance >= num.maxBuyIn;
            return current_balance >= num.maxBuyIn && tableInfo.maxPlayers == num.maxPlayers;
        });

        logger.info("Thông tin bàn tìm được : ", _arrTable);

        var _tblInfo;

        if (_arrTable.length > 0) {
            var _arrSortByPlayers   = _.sortBy(_arrTable, 'players');
            //_tblInfo = _arrSortByPlayers[0];
            // lấy ngẫu nhiên 1 table để trả về
            var _tblInfo = _arrSortByPlayers[Math.floor(Math.random() * _arrSortByPlayers.length)];

            _tblInfo = {data: _tblInfo, tid: _tblInfo.id};
        } else {
            // không có bàn phù hợp => tạo bàn mới
            var tblInfoCreate = me.getInfoTableCreate(user.balance, tableInfo.maxPlayers);
            // tblInfoCreate.zone = zone;
            logger.info(">> tblInfoCreate : ", tblInfoCreate);
            me.createTable(session.uid, tblInfoCreate, function (e, tid) {
                // _tblInfo = {data: tblInfoCreate, tid: e};
                _tblInfo = {data: tblInfoCreate, tid: tid};
            });
        }

        cb({
            code: CODE.OK,
            data: _tblInfo.data, // trả về thông tin bàn chơi
            tid: _tblInfo.tid // trả về id của tables sẽ được vào
        });
        return;
    } // end if else _tablesTotal == 0
};


/**
 * Add member to the table
 *
 * @param {Object} tid id of an existing table
 * @param {function} cb callback
 */
TableService.prototype.addMember = function(tid, uid, cb) {
    logger.info("[addMember] >> tid: ", tid, ' -> uid: ', uid);

    var me = this;
    var channelService = me.app.get('channelService');
    var table = this.tables[tid];
    if(!table){
        cb('table-not-found');
        return;
    }

    /**
     * Kiểm tra xem uid mới này có đang ngồi chầu rìa hay đang ngồi bàn hay đang chơi không
     * - Nếu có thì remove uid này trước
     */
    me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
    //UserStore.getByAttr('id', uid, false, function(e, user){
    //userDao.getUserById(uid, function(e, user) {
        logger.info("user trong cached with uid ", uid ,": data:", user);
        const sid = user?.serverId ?? null;
        if (user == null){
            cb("user-not-exist-in-game");
            return false;
        }

        user = user.player;
        logger.info("-> app|| services || tablesServices || addMember");
        logger.info("----> users: " + JSON.stringify(user));

        /**
         * check playersToAdd , if = 0 then reset gameWinners = []
         */
        if (table.table.playersToAdd.length == 0) {
            table.table.gameWinners = [];
            logger.info("TH trong ban choi khong ai ngoi => reset gameWinner");
        }

        /**
         * nếu trong bàn không có người nào thì initGame mới
         */
        if (table.table.members.length == 0) {
            //logger.info("Trong Ban Khong co ai dang dung hay ngoi => table info: ", table);
            table.table.initNewGame();
        }

        if(!user){
            cb(e);
        }
        // var sid = await getSidByUid(uid, me.app);
        // const sid = user?.serverId ?? null;
        if(!sid){
            return cb('invalid-connector-server');
        }
        // TODO: reduce payload by handling based on game state
        var channel = channelService.getChannel(tid, true);
        channel.add(uid, sid);

        /**
         * - khi có người chơi vào bàn
         * + TH JOIN: bàn chưa chơi => trả về danh sách nguồi chơi đang ngồi trong bàn
         * + TH IN_PROGRESS: bàn đang chơi => trả về danh sách người đang chơi + người đang ngồi chờ
         */
        var listMembers = me.getPlayersJSON(tid, 'playersToAdd', uid);
        var players         = me.getPlayersJSON(tid, 'players', uid);
        let playersToAdd    = [];
        if (table.state === "IN_PROGRESS") {
            // var players         = me.getPlayersJSON(tid, 'players', uid);
            playersToAdd = me.getPlayersJSON(tid, 'playersToAdd', uid);
            listMembers = _.union(players, playersToAdd);
        }
        logger.info("MINH>>JOIN_TABLE>>TableJSON: ", me.getTableJSON(tid, uid));
        channelService.pushMessageByUids({
            route  : consts.GAME.ROUTER.JOIN_TABLE, //'JoinTable',
            members : listMembers,
            players: players,
            waitings: playersToAdd,
            board: me.getTableJSON(tid, uid)?.board ?? [],
            currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
            currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
            timestamp: Math.floor(Date.now() / 1000) // tạm để
        }, [{
            uid : uid,
            sid : channel.getMember(uid)['sid'] //sid
        }], function(e) {
            if(e){
                logger.error('JoinTable: unable to push members ', e);
            }

            logger.debug('initiated player '+uid+' into table '+tid+' with state '+table.state);

            var checkUser = checkUserInTable(user, table);
            if (checkUser == false){
                logger.info("user: ", user, " added into table: ", tid);
                table.table.members.push(user);
            }

            channel.pushMessage({
                route   : consts.GAME.ROUTER.UPDATE_USERS, //'onUpdateUsers',
                members : table.table.members
            });

            // update gameId to userCached
            // me.app.rpc.manager.userRemote.onUserJoin(null, uid, null, tid, function () {
            me.app.rpc.manager.userRemote.onUserJoin(null, uid, tid, null, function () {
                logger.info("[tableService -> addMember -> update ", uid ," play with gameId: ", tid);
            });

            // clearJobQueue
            // -------------------------------------------------------------------------------------
            me.app.rpc.manager.userRemote.clearJobQueue(null, uid, function () {});

            // cb(); // removed
            // tra ve danh sach những người chơi đã ngồi xuống bàn
            cb(me.getPlayersJSON(tid, 'playersToAdd', uid));

        });

    });
};

/**
 * Hàm kiểm tra user đã có thông tin trong bàn hay chưa
 * @param user
 * @param table
 * @return {boolean}
 */
var checkUserInTable = function (user, table) {
    for(var i=0;i<table.table.members.length;i+=1){
        if (table.table.members[i].id == user.id) {
            return true; // đã có thông tin trong bàn rồi
        }
    }
    return false; // chưa có thông tin => được quyền add vào
};

/**
 * Get the connector server id associated with the uid
 */
// var getSidByUid = async function(uid, app){
//     // var connector = dispatcher.dispatch(uid, app.getServersByType('connector'));
//     // if(connector){
//     //     return connector.id;
//     // }
//     // return null;
//     const sid = await new Promise((resolve, reject) => {
//         app.rpc.manager.userRemote.getUserCacheByUid('*', uid, function (user) {
//             logger.info("[getSidByUid] getSidByUid check uid: ", uid, ", user: ", user);
//             if (user) {
//                 sidOfUser = user?.serverId ?? null;
//                 // callback(null, true, data);
//                 resolve(sidOfUser);
//             }else{
//                 reject(err);
//                 // callback(null, false, data);
//             }
//         });
//     });
//     logger.info("[getSidByUid] getSidByUid check uid: ", uid, ", sid: ", sid);
//     return sid;
// };

/**
 * Hàm call function removePlayer được gọi từ tableRemote
 * @param tid
 * @param uid
 * @param cb
 */
TableService.prototype.removePlayerFromRemote = function (tid, uid, cb) {
    logger.info("[Start] removePlayerFromRemote with tid ", tid, " uid ", uid);
    var me = this;
    // var table = this.tables[tid];
    if(!me.tables[tid]){
        var e = 'table-not-found';
        logger.error('[removePlayerFromRemote] error removing player '+uid+' from table '+tid, e);
        cb(e);
        return;
    }

    var user = me.getPlayerJSON(tid, uid, 'players') || me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid, 'previousPlayers');
    if(user){
        logger.info("removePlayerFromRemote >> user ", user);
        me.tables[tid].table.removePlayer(uid);
    }
    cb(CODE.OK);
};

/**
 *
 * Remove member from the table
 *
 * @description khi 1 user rời khỏi bàm
 *
 * @param {Object} tid id của bàn (phải tồn tại)
 * @param {string} uid userId của user rời khỏi bàn
 * @param {function} cb callback
 *
 */
TableService.prototype.removeMember = function(tid, uid, cb){
    logger.info("[removeMember][step 1] uid: ", uid, " from table id: ", tid);
    var me = this;
    // var table = this.tables[tid];
    if(!me.tables[tid]) {
        var e = 'table-not-found';
        logger.error('[removeMember][error] removing player ' + uid + ' from table ' + tid, e);
        cb(e);
        return;
    }
    var channelService = me.app.get('channelService');
    var channel = channelService.getChannel(tid, false);

    var tmpMoney = 0;
    var user = me.getPlayerJSON(tid, uid, 'players') || me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid, 'previousPlayers');
    logger.info("[removeMember][step 2] user: ", user);
    if(user) {
        if (user.isState == true){
            // đang ngồi
            tmpMoney = user.chips
        } else {
            // đã đứng
            tmpMoney = 0;
        }
        //console.log('adding '+user.chips+' to player '+user.id);
        logger.info('[removeMember] >> adding '+ tmpMoney +' to player '+ user.id);
        logger.info("[removeMember] >> Player get from table: ", user);
        me.updatePlayerInfo(user.id, {
            chips : 0, //user.chips,
            balance: tmpMoney, //user.chips
            gameId: "",
            isPlayer: false
        }, function(e, updatedUser) {
            if(e) {
                logger.error('[tableService.removeMember][updatePlayerInfo][error] removing player '+ uid +' with error: ', e);
                //cb();
                //return;
            } else {
                logger.debug('[tableService.removeMember][updatePlayerInfo] >> removed player '+ uid +' from table ' + tid, ' -> updatedUser: ', updatedUser);
            }

            me.tables[tid].table.removePlayer(uid);

            // Xoá mọi bộ đếm action của user khi ra khỏi bàn
            // ----------------------------------------------------------------------------------------------------
            me.app.rpc.manager.userRemote.clearJobQueueActionOfPlayerId(null, tid, uid, function (err, resQ) {
                logger.info("[removeMember][clearJobQueueActionOfPlayerId] >> tid: ", tid, " -> uid: ", uid, " -> resQ: ", resQ, " -> err: ", err);
            });

            // push to bot
            me.tables[tid].table.eventEmitter.emit('playerLeaved');

            // push thông tin về cho chính mình để cập nhật lại dữ liệu
            // ----------------------------------------------------------------------------------------------------
            me.pushPlayerInfo2(tid, uid, channelService, channel, function (e) {
                logger.info("[removeMember][pushPlayerInfo2][TH1] >> tid: ", tid, " -> uid: ", uid, " -> e: ", e);
                if(channel && channel.getMember(uid)){
                    channel.leave(uid, channel.getMember(uid)['sid']);
                }
            });


            //me.pushPlayerInfo(tid, uid, updatedUser); // hiện tai ko push về được do đã bị xoá khỏi chanel ở trên
            //me.handleGameState(tid, cb);  // remove
            // added ------

            //if(me.tables[tid].table && me.tables[tid].table.game && me.tables[tid].table.game.roundName == 'GameEnd' && table.state == 'IN_PROGRESS' && me.tables[tid].table.active){
            //    me.endGame(tid, cb);
            //}
            logger.info("[removeMember] >> me.tables[", tid ,"].state ", me.tables[tid].state);
            if(me.tables[tid].state !== 'IN_PROGRESS') {
                //me.handleGameState(tid, cb);

                // khai báo giá trị của mảng người nhận
                //var playerReceivers = [];
                // khai báo mảng dữ liệu trả về
                var dataReturn = [];

                // lấy danh sách người chơi đang ngồi
                // ----------------------------------------------------------------------------------------
                var playertoAdd = me.getPlayersJSON(tid, 'playersToAdd', uid);
                logger.info("[removeMember] >> state != IN_PROGRESS >> playertoAdd: ", playertoAdd);

                // lấy danh sách không đủ tiền
                // ----------------------------------------------------------------------------------------
                var playertoNotEnough = me.getPlayersJSON(tid, 'playersToNotEnough', uid);

                // lấy danh sách người chơi đang đứng
                // ----------------------------------------------------------------------------------------
                var playerStandUp = me.tables[tid].table.members; // toàn bộ người chơi trong bàn (cà ngồi, cả chơi, cả đứng)
                logger.info("[removeMember] >> state != IN_PROGRESS >> playerStandUp(allmember): ", playerStandUp);

                // uniq danh sách mảng
                var playersUniq = _.union(playertoAdd, playerStandUp);
                playersUniq = _.uniq(playersUniq, function(player) {
                    return player.id;
                });
                logger.info("[removeMember] >> state != IN_PROGRESS >> playersUniq: ", playersUniq);

                // Lấy danh sách người chơi không đủ tiền dạng sid + uid
                // ----------------------------------------------------------------------------------------
                playertoNotEnough   = me.getReceiver(playertoNotEnough, channel);
                logger.info("[removeMember][JOIN] playertoNotEnough: ", playertoNotEnough);

                // Danh sách players sẽ nhận messages
                // ----------------------------------------------------------------------------------------
                var playerReceivers = me.getReceiver(playersUniq, channel);
                logger.info("[removeMember][JOIN] playerReceivers: ", playerReceivers);

                // Lọc danh sách nhận messages bỏ qua những người không đủ tiền
                // ----------------------------------------------------------------------------------------
                playerReceivers     = _.difference(playerReceivers, playertoNotEnough);
                playerReceivers     = _.uniq(playerReceivers, function(x) {
                    return x.uid;
                });

                logger.info("[removeMember][JOIN] playerReceivers Sau: ", playerReceivers);
                logger.info("[removeMember][JOIN] Danh Sach Dang Ngoi : ", playertoAdd.length);
                logger.info("[removeMember][JOIN] Danh Sach Tong (dang ngoi + dang dung) : ", playerStandUp.length);

                // Nếu danh sách nhận có dữ liệu thì push message về cho họ
                if (playerReceivers.length != 0) {
                    dataReturn = playertoAdd;
                    // me.handleGamePushMessage(tid, playerReceivers, dataReturn, cb);
                    me.handleGamePushMessage(tid, playerReceivers, dataReturn, function (e) {
                    });
                }

                // Trường hợp trong bàn không còn ai đứng hoặc ngồi thì xoá bàn
                if (playerStandUp.length <= 0){
                    logger.info("[removeMember][JOIN] Khong co ai trong ban : ", playerStandUp.length, " => xoa ban + xoa clear table");
                    // TH không còn ai trong bàn nữa => clear job start and xoá bàn
                    // -----------------------------------------------------------------------------------------
                    me.app.rpc.game.tableRemote.clearJobStartGame(null, tid, function () {
                        // delete table with tid
                        // -------------------------------------------------------------------------------------
                        // delete me.tables[tid];
                    });
                }

                // Trả dữ liệu về : những người đang ngồi
                //dataReturn = playertoAdd;
                //me.handleGamePushMessage(tid, playerReceivers, dataReturn, cb);
            }
            // else{
            //     // TH bàn đang chơi -> bàn đang chơi thì ko cần làm gì cả
            //     // lấy danh sách người chơi đang ngồi
            //     // -------------------------------------------------------------------------------------
            //     var playertoAdd = me.getPlayersJSON(tid, 'playersToAdd', uid);
            //
            //     // lấy danh sách người chơi đang đứng
            //     // -------------------------------------------------------------------------------------
            //     var allMembers = me.tables[tid].table.members; // toàn bộ người chơi trong bàn (cà ngồi, cả chơi, cả đứng)
            //
            //     // uniq danh sách mảng
            //     // -------------------------------------------------------------------------------------
            //     var playersUniq = _.union(playertoAdd, allMembers);
            //     var playerReceivers = me.getReceiver(playersUniq, channel);
            //
            //     logger.info("[removeMember][IN_PROGRESS] Danh Sach Dang Ngoi : ", playertoAdd.length);
            //     logger.info("[removeMember][IN_PROGRESS] Danh Sach Tong (dang ngoi + dang dung) : ", playerReceivers.length);
            //     if (playerReceivers.length == 0) {
            //         // TH không còn ai trong bàn nữa => clear job start and xoá bàn
            //         // -------------------------------------------------------------------------------------
            //         me.app.rpc.game.tableRemote.clearJobStartGame(null, tid, function () {
            //             // delete table with tid
            //             // -------------------------------------------------------------------------------------
            //             delete me.tables[tid].game;
            //             delete me.tables[tid];
            //
            //         });
            //     }
            // }

            // end added
            cb();
            return;
        });
    } else {
        /**
         * Trường hợp người chơi đang đứng xem mà thoát ra khỏi bàn thì
         * - cập nhật lại gameId = null
         */

        me.tables[tid].table.removePlayer(uid);

        // push thông tin về cho chính mình để cập nhật lại dữ liệu
        // ---------------------------------------------------------------------------------------------------
        me.pushPlayerInfo2(tid, uid, channelService, channel, function (e) {
            logger.info("[removeMember][pushPlayerInfo2][TH2] -> tid: ", tid, " -> uid: ", uid, " -> e: ", e);
            if (e == "done"){
                //console.log("eeeee: ", e);
                logger.info("[removeMember] -> me.pushPlayerInfo2: ", e);
                if(channel && channel.getMember(uid)){
                    channel.leave(uid, channel.getMember(uid)['sid']);
                }
            }
        });

        /**
         * TH ket nick
         * check total member in tables , if total == 0 => reset playersToAdd = [], gameWinners = []
         */
        logger.info("[removeMember] -> me.tables[tid].table: ", me.tables[tid].table);
        logger.info("[removeMember] -> me.tables[tid].table.playersToAdd.length: ", me.tables[tid].table.playersToAdd.length);
        if (me.tables[tid].table.playersToAdd.length == 0) {
            logger.info("[removeMember] ban khong co ai dang ngoi => reset playersToAdd, gameWinners");
            me.tables[tid].table.playersToAdd = [];
            me.tables[tid].table.gameWinners = [];
        }
        cb();
    }
};

/**
 * Hàm đầy user ra khỏi phòng, để nhảy sang phòng khác khi:
 * - đang chơi
 * - đang đứng
 * - đang ngồi
 * @param tid
 * @param uid
 * @param cb
 */
/*
TableService.prototype.clearPlayerOutTable = function (tid, uid,cb){
    var me = this;
    // check user đó đang làm gì
    var user = me.getPlayerJSON(tid, uid, 'players') || me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid, 'previousPlayers');
    if(user){
        //đang ngồi chơi hoặc đang ngồi chờ

    }else{
        // đang chầu rìa

    }
};
*/


/**
 *
 * Remove Member when player stand up
 *
 * @param tid
 * @param uid
 * @param cb
 */
TableService.prototype.removeMemberStandUp = function(tid, uid, cb){
    logger.info("app||services||tableService => removeMemberStandUp");
    var me = this;
    var table = this.tables[tid];
    if(!me.tables[tid]){
        var e = 'table-not-found';
        logger.error('[standup] error removing player '+uid+' from table '+tid, e);
        cb(e);
        return;
    }
    var channelService = me.app.get('channelService');
    var channel = channelService.getChannel(tid, false);

    var user = me.getPlayerJSON(tid, uid, 'players') || me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid, 'previousPlayers');

    if(user){
        logger.info('[standup] adding '+user.chips+' to player '+user.id);
        me.updatePlayerInfo(uid, {
            //chips : user.chips
            chips : 0,
            balance: user.chips,
            isPlayer: false
        }, function(e, updatedUser){
            if(e){
                logger.error('[tableService.removeMemberStandUp][error] removing player '+uid+' from table ', e);
            }else{
                logger.debug('[tableService.removeMemberStandUp] removed player ' + uid +' from table ' + tid + ' -> updatedUser: ', updatedUser);
            }

            //me.tables[tid].table.removePlayer(uid);
            me.tables[tid].table.standUpPlayer(uid);

            // update
            me.pushPlayerInfo2(tid, uid, channelService, channel, function (e) {});
            //me.pushPlayerInfo(tid, uid, updatedUser); // remove thay đổi data trả về
            //me.handleGameState(tid, cb);

            /**
             * push message
             * Mặc định là bàn chưa chơi -> mảng nhận message là những người đang ngồi + những người đang đứng
             * mảng gồm uid, sid
             */
            // khai báo giá trị của mảng người nhận
            var playerReceivers = [];
            // khai báo mảng dữ liệu trả về
            var dataReturn = [];

            // lấy danh sách người chơi đang ngồi
            // ----------------------------------------------------------------------------------------
            var playertoAdd = me.getPlayersJSON(tid, 'playersToAdd', uid);

            // lấy danh sách người chơi đang đứng (toàn bộ người chơi trong bàn)
            // ----------------------------------------------------------------------------------------
            var playerStandUp = table.table.members;

            // lấy danh sách không đủ tiền
            // ----------------------------------------------------------------------------------------
            var playertoNotEnough = me.getPlayersJSON(tid, 'playersToNotEnough', uid);

            // Lấy danh sách người chơi không đủ tiền dạng sid + uid
            // ----------------------------------------------------------------------------------------
            playertoNotEnough   = me.getReceiver(playertoNotEnough, channel);

            // uniq danh sách mảng
            // ----------------------------------------------------------------------------------------
            var playersUniq = _.union(playertoAdd, playerStandUp);
            playerReceivers = me.getReceiver(playersUniq, channel);

            // Lọc danh sách nhận messages bỏ qua những người không đủ tiền
            // ----------------------------------------------------------------------------------------
            playerReceivers     = _.difference(playerReceivers, playertoNotEnough);
            playerReceivers     = _.uniq(playerReceivers, function(x){
                return x.uid;
            });

            if (playerReceivers.length == 0) {
                var receiverTmp = {uid: uid, sid: channel.getMember(uid)['sid']};
                playerReceivers.push(receiverTmp);
            }

            // Trả dữ liệu về : những người đang ngồi
            dataReturn = playertoAdd;

            if (table.state === "IN_PROGRESS") {
                // Th: đang chơi -> push thêm về cho chính mình + những người đang ngồi chờ
                var receiverTmp = {uid: uid, sid: channel.getMember(uid)['sid']};
                // Thêm mình vào danh sách nhận message
                playerReceivers = [];
                playerReceivers.push(receiverTmp);

                // Trả dữ liệu về : những người đang chơi trong bàn
                dataReturn = me.getPlayersJSON(tid, 'players', uid);
            }

            logger.debug("[standUp] All players: ", playerStandUp);
            logger.debug("[standUp] Player sau khi loc >> playersUniq: ", playersUniq);
            logger.debug("[standUp] playerReceivers: ", playerReceivers);
            logger.debug("[standUp] dataReturn: ", dataReturn);

            // me.handleGamePushMessage(tid, playerReceivers, dataReturn, cb);
            me.handleGamePushMessage(tid, playerReceivers, dataReturn, function (e) {
            });

            cb();

        });
    } else {
        //me.tables[tid].table.removePlayer(uid);
        me.tables[tid].table.standUpPlayer(uid);

        // added - cập nhật lại dữ liệu cho chính mình
        me.pushPlayerInfo2(tid, uid, channelService, channel, function (e) {});

        /**
         * me.app.get('channelService').getChannel(tid, true).pushMessage({
                route   : consts.GAME.ROUTER.UPDATE_USERS, //'onUpdateUsers',
                members : table.members
            });
         */

        /**
         * TH ket nich
         * check total member in tables , if total == 0 => reset playersToAdd = [], gameWinners = []
         */
        if (me.tables[tid].table.playersToAdd.length == 0) {
            logger.info("[standup] ban khong co ai dang ngoi => reset playersToAdd, gameWinners");
            me.tables[tid].table.playersToAdd = [];
            me.tables[tid].table.gameWinners = [];
        }

        cb();
    }
};

/**
 * Kiểm tra có phải đang là turn của mình hay không
 * @param tid table id
 * @param uid uid cua người chơi cần check
 * @param currentPlayer vị trị của người chơi đang có turn hiện tại
 * @return {boolean} true|false
 */
TableService.prototype.checkMyTurn = function (tid, uid, currentPlayer) {
    var players = this.tables[tid].table.players;
    for(var i=0;i<players.length;i+=1){
        if (players[i].id === uid && players[i].id === players[currentPlayer].id){
            return true; // my turn
        }
    }
    return false; // not my turn
};


TableService.prototype.handleGamePushMessage = function(tid, Receivers, data, cb) {
    logger.info("app||servcies||tableService||handleGamePushMessage------------------------");
    logger.info("app||servcies||tableService||handleGamePushMessage tid: ", tid);
    logger.info("app||servcies||tableService||handleGamePushMessage Receivers: ", Receivers);
    logger.info("app||servcies||tableService||handleGamePushMessage data: ", data);
    var me = this;
    var channelService = me.app.get('channelService');

    // Check if Receivers array is empty
    let board = [];
    let currentPlayerTime = 0;
    let currentPlayer = null;
    if (Receivers || Receivers.length > 0) {
        // Get random Receivers from array
        const randomIndex = Math.floor(Math.random() * Receivers.length);
        const randomReceiver = Receivers[randomIndex];
        logger.info("app||servcies||tableService||handleGamePushMessage randomReceiver: ", randomReceiver);
        const tableJsonData = me.getTableJSON(tid, randomReceiver) ?? null; // ?.board ?? [];
        logger.info("app||servcies||tableService||handleGamePushMessage tableJsonData: ", tableJsonData);
        board = tableJsonData?.board ?? [];
        currentPlayerTime = tableJsonData?.currentPlayerTime ?? 0
        currentPlayer = tableJsonData?.currentPlayer ?? null;
    }

    var table = me.tables[tid];
    if(table.table && table.table.game && table.table.game.roundName == 'GameEnd' && table.state == 'IN_PROGRESS' && table.table.active) {
        logger.info("app||servcies||tableService||handleGamePushMessage table.table.game.roundName: ", table.table.game.roundName);
        me.endGame(tid, cb);
    } else {
        channelService.pushMessageByUids({
            route: consts.GAME.ROUTER.JOIN_TABLE,
            members: data,
            players: me.getPlayersJSON(tid, 'players', Receivers.length > 0 ? Receivers[0].uid : null), // Thêm danh sách người đang chơi
            waitings: me.getPlayersJSON(tid, 'playersToAdd', Receivers.length > 0 ? Receivers[0].uid : null),
            board: board, // trả về thông tin board (lá bài trên bàn)
            currentPlayerTime: currentPlayerTime, // tra ve thoi gian cua nguoi choi hien tai
            currentPlayerIndex: currentPlayer, // tra ve vi tri cua nguoi choi hien tai
            timestamp: Math.floor(Date.now() / 1000) // tạm để
        }, Receivers, function (e) {
            logger.info("app||servcies||tableService||handleGamePushMessage e: ", e);
            if (e) {
                logger.error(consts.GAME.ROUTER.JOIN_TABLE, ': unable to push members ', e);
            }
            logger.info("app||servcies||tableService||handleGamePushMessage send [ok] ");
            logger.info("app||servcies||tableService||handleGamePushMessage state: ", me.tables[tid].state);
            logger.info("app||servcies||tableService||handleGamePushMessage active: ", me.tables[tid].table.active);

            //if(i == me.tables[tid].table.members.length){
                if(me.tables[tid].state == 'IN_PROGRESS' && me.tables[tid].table.active) {
                    me.tables[tid].table.startTimer();
                }
                //return;
            //}

            // cb();
            cb(null);
        });
    }
};

/**
 * Update player information
 *
 * @param {string} uid id of a user to update
 * @param {object} obj updated player information
 * @param {function} cb callback
 *
 */
TableService.prototype.updatePlayerInfo = function(uid, obj, cb){
    logger.info("[tableService]->updatePlayerInfo -> uid: ", uid, " => obj: ", obj);
    var me = this;
    //UserStore.getByAttr('id', uid, false, function(e, user){
    me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
        /*
        if(e){
            return cb(e);
        }
        */
        logger.info("[tableService]->updatePlayerInfo -> user: ", user, " => obj: ", obj);

        if(!user){
            return cb('user-not-found');
        }
        var userObj = {
            //id : user.id
            uid : user.uid
        };
        if(obj.chips && typeof obj.chips === 'number' && obj.chips != 0){
            userObj.chips = Math.round(user.chips + Math.round(obj.chips));
            //userObj.chips = Math.round(obj.chips);
        }
        // cập nhật lại tổng số tiền đang có
        if(obj.balance && typeof obj.balance === 'number' && obj.balance != 0){
            userObj.balance = Math.round(user.player.balance + Math.round(obj.balance));
        }
        if(obj.wins){
            userObj.wins = Math.round(user.wins + Math.round(obj.wins))
        }
        if(obj.wonAmount && obj.wonAmount > user.largestWin){
            userObj.largestWin = obj.wonAmount;
        }
        if (obj.roomId){
            userObj.roomId = obj.roomId;
        }
        logger.info("obj.gameId: ", obj.gameId);
        if (obj.gameId !== undefined && obj.gameId !== null){
            logger.info("obj.gameId bentrong: ", obj.gameId);
            userObj.gameId = obj.gameId;
        }
        logger.info("set isPlayer = ", obj.isPlayer);
        userObj.isPlayer = obj.isPlayer;

        me.app.rpc.manager.userRemote.onUpdateMoneyGameByUid(null, userObj, function (e, updatedUser) {
            if(e){
                cb(e);
                return;
            }
            cb(null, updatedUser);
        });

    });
};

TableService.prototype.getTableJSON = function(tid, uid){
    if(!this.tables[tid]){
        return;
    }
    var table = this.tables[tid];
    logger.info("[tableService.getTableJSON] tid: ", tid, " => table.table: ", table.table);
    return {
        state           : table.state,
        id              : (table.table && table.table.game && table.table.game.id ? table.table.game.id : undefined),
        tid             : tid,
        creator         : table.creator,
        dealer          : table.table.dealer,
        smallBlind      : table.table.smallBlind,
        bigBlind        : table.table.bigBlind,
        minPlayers      : table.table.minPlayers,
        maxPlayers      : table.table.maxPlayers,
        minBuyIn        : table.table.minBuyIn,
        maxBuyIn        : table.table.maxBuyIn,
        gameMode        : table.table.gameMode,
        isShowBoard     : table.table.isShowBoard,
        players         : this.getPlayersJSON(tid, 'players', uid),
        playersToRemove : this.getPlayersJSON(tid, 'playersToRemove', uid),
        playersToAdd    : this.getPlayersJSON(tid, 'playersToAdd', uid),
        gameWinners     : this.getPlayersJSON(tid, 'gameWinners', uid),
        playersToNotEnough     : this.getPlayersJSON(tid, 'playersToNotEnough', uid),
        actions         : table.table.actions,
        game            : stripDeck(table.table.game, ['deck', 'id']),
        board           : (table.table.game && table.table.game.board) ? table.table.game.board : [],
        currentPlayer   : table.table.currentPlayer,
        currentPlayerTime: table.table.currentPlayerTime, // 11/02/2025 => added
    };
};

function stripDeck(obj, props){
    var out = {};
    for(var key in obj){
        if(props.indexOf(key) == -1){
            out[key] = obj[key];
        }
    }
    return out;
}

TableService.prototype.getPlayerIndex = function(tid, uid, type){
    var match;
    if(!this.tables[tid]){
        return;
    }
    for(var i=0;i<this.tables[tid].table[type ? type : 'players'].length;++i){
        if(uid == this.tables[tid].table[type ? type : 'players'][i].id){
        /*
        var strUid = _.isNumber(uid) ? uid.toString() : uid;
        var _uid = this.tables[tid].table[type ? type : 'players'][i].id;
        var strId = _.isNumber(_uid) ? _uid.toString() : _uid;
        var check = strUid.localeCompare(strId);
        if(check == 0){
        */
            match = i;
        }
    }
    //console.log("[app||services||tableServices||getPlayerIndex --> uid: ", uid, " => match: ",match);
    return match;
};

/**
 * Check ActorNumber is Available or not available
 * @param tid table id
 * @param actorNr actorNr position from client post
 * @param type
 * @return {boolean} true if is exists, false if is available
 */
TableService.prototype.checkPosAvailable = function (tid, actorNr, type) {

    if(!this.tables[tid]){
        return;
    }

    for(var i=0;i<this.tables[tid].table[type ? type : 'players'].length;++i){
        if(actorNr == this.tables[tid].table[type ? type : 'players'][i].actorNr){

            return true;
        }
    }

    return false;
    //console.log("checkPosAvailable ==========: ", this.tables[tid].table[type ? type : 'players']);
};

/**
 * example: hand: { cards: [ '8S', '7D', 'QS', '5D', '6D', '3C', '4S' ], rank: 116,message: 'Straight' }
 * @param tid
 * @param uid
 * @param type
 * @param requestUid
 * @return {*}
 */
TableService.prototype.getPlayerJSON = function(tid, uid, type, requestUid) {
    logger.info("[tableService.getPlayerJSON] => tid: ", tid, ", uid: ", uid, ", type: ", type, ", requestUid: ", requestUid);
    if(!this.tables[tid]){
        return;
    }
    var playerIndex = this.getPlayerIndex(tid, uid, type);
    var player = this.tables[tid].table[type ? type : 'players'][playerIndex];
    logger.info("[tableService.getPlayerJSON] player: ", player, ", playerIndex: ", playerIndex);
    return player ? {
        playerName : player.playerName,
        id         : player.id,
        chips      : player.chips,
        folded     : player.folded,
        allIn      : player.allIn,
        talked     : player.talked,
        amount     : player.amount,
        type       : player.type, // check login theo fb|device||acc
        avatar     : player.avatar,
        level      : player.level,
        vippoint   : player.vippoint,
        exp        : player.exp, // added lấy ra điểm kinh nghiệm
        actorNr    : player.actorNr,
        index      : playerIndex,
        isState    : player.isState, // trang thai dung| ngoi
        hand       : player.hand,
        players    : player.players, // danh sách người chơi cùng + số bài trên tay họ
        broad      : player.broad, // trả về thêm số quân bài broad -> khi kết thúc ván
        cards      : (typeof requestUid === 'undefined' || player.id == requestUid) ? player.cards : undefined,
    } : undefined;
};

TableService.prototype.getPlayersJSON = function(tid, type, requestUid){
    var players = [];
    if(!this.tables[tid]){
        return;
    }
    for(var i=0;i<this.tables[tid].table[type ? type : 'players'].length;++i){
        players.push(this.getPlayerJSON(tid, this.tables[tid].table[type ? type : 'players'][i].id, type, requestUid));
    }
    return players;
};


/**
 *
 * Add a player to the game
 * Ngồi xuống ghế
 *
 * @param {Object} tid id of an existing table
 * @param {string} uid userId to add to the table
 * @param {number} buyIn amount to buy in
 * @param {function} cb callback
 *
 */
//TableService.prototype.addPlayer = function(tid, uid, buyIn, cb){
TableService.prototype.addPlayer = function(tid, uid, buyIn, index, cb){
    logger.info("[tableService.addPlayer] uid ", uid, " in actorNr: ", index, " in tid ", tid, " buyIn: ", buyIn);
    var me = this;
    var _cbRes = {};
    if(!this.tables[tid]) {
        _cbRes = {
            msg: "table-not-found",
            code: CODE.TABLE.TABLE_NOT_FOUND
        };
        //return cb('table-not-found');
        return cb(_cbRes);
    }

    var table = this.tables[tid].table;

    if (typeof index === "undefined") {
        logger.info("Invaild actorNr ", index);
        _cbRes = {
            msg: "invalid-actorNr",
            code: CODE.TABLE.INVALID_ACTORNR
        };
        //return cb("invalid-actorNr");
        return cb(_cbRes);
    }

    logger.info("=> Trang thai isSitDown: ", table.isSitDown);

    // 27.02.2025 - bỏ chặn trạng thái isSitDown
    // if (table.isSitDown) {
    //     logger.info("Đang xử lý ngồi cho người khác uid ", uid, " với actorNr: ", index, " đợi nhé");
    //     _cbRes = {
    //         msg: "action-sitdown-is-active",
    //         code: CODE.TABLE.ACTION_SITDOWN_ACTIVE
    //     };
    //     //return cb("action-sitdown-is-active");
    //     return cb(_cbRes);
    // }
    // bắt đầu set chế độ đang hoạt động
    // -----------------------------------------------------------------------------------------------------------
    table.isSitDown = true;

    if(me.getPlayerIndex(tid, uid, 'playersToAdd')) {
        logger.info("addPlayer >> getPlayerIndex with uid ",uid," already-joined ---");
        table.isSitDown = false;
        _cbRes = {
            msg: "already-joined",
            code: CODE.TABLE.ALREADY_JOINED
        };
        //return cb('already-joined');
        return cb(_cbRes);
    }
    // logger.info("addPlayer >> getPlayerIndex before buyIn ",buyIn);
    buyIn = parseInt(buyIn);

    // Đưa phần này xuống dưới để check điều kiện người chơi đã chơi ván trước thì cho phép buyin số tiền thấp hơn min buyin của bàn
    // logger.info("addPlayer >> getPlayerIndex >> buyIn: ",buyIn, ' -> table.minBuyIn: ', table.minBuyIn, ' -> table.maxBuyIn: ', table.maxBuyIn);
    // if(isNaN(buyIn) || buyIn < table.minBuyIn || buyIn > table.maxBuyIn) {
    //     let isExistingInPreviousPlayer = _.some(table.previousPlayers, player => player.id === user.id);
    //     logger.info("[addPlayer]-> isExistingInPreviousPlayer: ", isExistingInPreviousPlayer);

    //     table.isSitDown = false;
    //     _cbRes = {
    //         msg: "invalid-buyin",
    //         code: CODE.TABLE.INVALID_BUYIN
    //     };
    //     return cb(_cbRes);
    // }

    buyIn = Math.round(buyIn);

    // Kiếm tra xem vị trí ghế ngồi còn trống không
    // Nếu trả về true => vị trí đã có người ngồi
    // Nếu trả về false => được ngồi
    var isState, playerToAddCheck, playerCheck;

    playerToAddCheck    = _.find(this.tables[tid].table['playersToAdd'], function(item) { return item.actorNr == index });
    playerCheck         = _.find(this.tables[tid].table['players'], function(item) { return item.actorNr == index });

    logger.info("app||services||tableService||addPlayer->playerToAddCheck: ", playerToAddCheck);
    logger.info("app||services||tableService||addPlayer->playerCheck: ", playerCheck);

    if (typeof playerCheck !== "undefined" || typeof playerToAddCheck !== "undefined") {
        logger.info("app||services||tableService|| position (", index, ") not Available");
        table.isSitDown = false;
        // return cb('actorNr-notAvailable');
        _cbRes = {
            msg: "invalid-actorNr",
            code: CODE.TABLE.INVALID_ACTORNR
        };
        return cb(_cbRes);
    }

    /**
     * Kiểm tra uid có nằm trong danh sách đang ngồi hay không
     * Nếu có rồi thì return luôn
     * Nếu chưa có thì cho qua :D
     */
    //var checkUid = checkUserInToPlayersToAdd(table, uid);
    var checkUid = _.findWhere(table.playersToAdd, {id: uid});
    if (checkUid){
        logger.info("app||services||tableService||addPlayer2->uid already-joined---");
        table.isSitDown = false;
        //return cb('user-is-already-joined');
        _cbRes = {
            msg: "already-joined",
            code: CODE.TABLE.ALREADY_JOINED
        };
        return cb(_cbRes);
    }

    /**
     * Check xem uid này có đang chơi game hay không, nếu đang chơi mà đứng dậy, thì ko cho ngồi lại
     */
    //var checkUidPlay = me.checkUserInToPlayers(table, uid);
    var checkUidPlay = _.findWhere(table.players, {id: uid});
    if(checkUidPlay){
        logger.info("app||services||tableService||addPlayer->user-is-playing---");
        table.isSitDown = false;
        //return cb('user-is-playing');
        _cbRes = {
            msg: "user-is-playing",
            code: CODE.TABLE.USER_IS_PLAYING
        };
        return cb(_cbRes);
    }

    logger.info("IN DANH SACH ADD_TO_PLAY:", table.playersToAdd);

    //UserStore.getByAttr('id', uid, false, function(e, user){
    me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
    // userDao.getPlayer(uid, function (user) {
        logger.info("app||services||tableService||addPlayer->user: ", user);
        // check user co ton tai hay khong
        if (!user) {
            logger.info("user lay tu userCached : ", user);
            table.isSitDown = false;
            // cb('user-not-found');
            // return;
            _cbRes = {
                msg: "user-not-found",
                code: CODE.TABLE.USER_NOT_FOUND
            };
            return cb(_cbRes);
        }

        // Kiểm tra xem người chơi có chơi ở ván trước đó hay không theo tham số previousPlayers
        const previousPlayers = me.getPlayersJSON(tid, 'previousPlayers', uid);
        const isExistingInPreviousPlayer = _.some(previousPlayers, player => player.id === user.uid);
        // const isExistingInPreviousPlayer2 = _.some(previousPlayers, player => player.id === user.uid);
        logger.info("[addPlayer] >> isExistingInPreviousPlayer: ", isExistingInPreviousPlayer, ' -> user.uid: ', user.uid);
        logger.info("[addPlayer] >> previousPlayers11111: ", previousPlayers);
        // logger.info("[addPlayer] >> table.previousPlayers: ", table.previousPlayers);
        logger.info("[addPlayer] >> getPlayerIndex >> buyIn: ",buyIn, ' -> table.minBuyIn: ', table.minBuyIn, ' -> table.maxBuyIn: ', table.maxBuyIn);
        if(isNaN(buyIn) || (buyIn < table.minBuyIn || buyIn > table.maxBuyIn)) {
            if (!isExistingInPreviousPlayer) {
                table.isSitDown = false;
                _cbRes = {
                    msg: "invalid-buyin",
                    code: CODE.TABLE.INVALID_BUYIN
                };
                return cb(_cbRes);
            }
        }

        // check xem uid co dang choi game o ban khac hay khong
        logger.info("addPlayer: user.isPlayer = ", user.isPlayer, " and tid ", tid);
        //if (user.gameId !== null){
        if (user.isPlayer === true){
            logger.info("addPlayer: user.uid ", user.player.id, " playing in table ", user.gameId);
            table.isSitDown = false;
            // cb('playing-in-other-table');
            // return;
            _cbRes = {
                msg: "playing-in-other-table",
                code: CODE.TABLE.PLAYING_IN_OTHER_TABLE
            };
            return cb(_cbRes);
        }

        user = user.player;
        logger.info("addPlayer >> user.balance: ", user.balance, ' >> table.minBuyIn: ', table.minBuyIn, ' >> buyIn: ', buyIn);
        //if(Math.round(user.chips) < table.minBuyIn){
        if(Math.round(user.balance) < table.minBuyIn && !isExistingInPreviousPlayer) {
            table.isSitDown = false;
            // cb('below-minimum-buyin');
            // return;
            _cbRes = {
                msg: "below-minimum-buyin",
                code: CODE.TABLE.BELOW_MININUM_BUYIN
            };
            return cb(_cbRes);
        }

        // logger.info("[addPlayer]-> table>>players: ", me.tables[tid].table['players']);
        // logger.info("[addPlayer]-> table>>players2: ", me.getPlayersJSON(tid, 'players', uid));
        logger.info("[addPlayer]-> table>>previousPlayers: ", me.getPlayersJSON(tid, 'previousPlayers', uid));
        logger.info("[addPlayer]-> table>>playersToNotEnough: ", me.getPlayersJSON(tid, 'playersToNotEnough', uid));
        //if(Math.round(user.chips) < buyIn){
        if(Math.round(user.balance) < buyIn){
            // check xem người chơi có chơi ở ván trước đó hay không theo tham số previousPlayers
            // const isExistingPlayer = table.table.members.some(member => member.id === user.id);
            // const previousPlayers = me.getPlayersJSON(tid, 'previousPlayers', uid);
            logger.info("[addPlayer]-> previousPlayers: ", previousPlayers);
            // const isExistingInPreviousPlayer = _.some(table.previousPlayers, player => player.id === user.id);
            // logger.info("[addPlayer]-> isExistingInPreviousPlayer: ", isExistingInPreviousPlayer);

            if (!isExistingInPreviousPlayer) {
                table.isSitDown = false;
                _cbRes = {
                    msg: "not-enough-chips",
                    code: CODE.USER.NOT_ENOUGH_MONEY
                };
                return cb(_cbRes);
            }
            // If player is existing member, allow them to continue with available balance
            buyIn = Math.round(user.balance);
        }
        //var chips = Math.round(user.chips - buyIn);
        var chips = Math.round(user.balance - buyIn);
        logger.info("[tableService.addPlayer] chips: ", chips, ' -> buyIn: ', buyIn, ' -> user.balance: ', user.balance);
        //var _userUpdate = {uid: user.id, balance: chips, chips: buyIn, actorNr: index};
        // var _userUpdate = {uid: user.id, balance: chips, chips: buyIn, actorNr: index, gameId: tid, isPlayer:true};
        var _userUpdate = {uid: user.id, balance: chips, chips: buyIn, actorNr: index, gameId: tid, isPlayer:true};

        logger.info("[addPlayer]->_userUpdate: ", _userUpdate);
        me.app.rpc.manager.userRemote.onUpdateMoneyGameByUid(null, _userUpdate, function (e, updatedUser) {
            logger.info("[addPlayer][onUpdateMoneyGameByUid]->updatedUser: ", updatedUser, ' -> e: ', e);
            if(e) {
                table.isSitDown = false;
                cb(e);
                return;
            }
            updatedUser = updatedUser.player;

            table.eventEmitter.emit('playerJoined');

            // cập nhật lại index - nhận dữ liệu vị trí ghé ngồi từ client gửi lên
            var mIndex = me.getPlayerIndex(tid, updatedUser.id, 'members');

            if(typeof mIndex === 'number'){
                table.members[mIndex].chips = chips;
            }

            // added logs
            logger.info("---> mIndex: " + mIndex  + " => vi tri:" + index);
            logger.info("---> addPlayer ", user);

            table.AddPlayer(index, updatedUser.nick_name, buyIn, uid, user.avatar, user.level, user.vippoint, user.exp, user.type);

            // remove
            me.pushPlayerInfo(tid, user.id, function (e) {});

            me.app.get('channelService').getChannel(tid, true).pushMessage({
                route   : consts.GAME.ROUTER.UPDATE_USERS, //'onUpdateUsers',
                members : table.members
            });

            /**
             *  Sau khi thêm người chơi thành công vào bàn, nếu bàn chưa chơi hay vừa kết thúc thì reset lại jobId
             *  Nếu bàn đang chơi thì bỏ qua
             */
            // -----------------------------------------------------------------------------------------------------

            // if (me.tables[tid].state === "JOIN" || me.tables[tid].state === "END_GAME") {
            //     me.app.rpc.game.tableRemote.clearJobStartGame(null, tid, function () {});
            // }

            var timerDefault = consts.GAME.TIMER.FIRST_START;
            if (me.tables[tid].state !== "JOIN") {
                timerDefault = consts.GAME.TIMER.WAIT_START;
            }

            /**
             * Khi có người chơi từ danh sách chầu rìa -> ngồi xuống
             * Push về cho chính người chơi ngồi xuống -> push về danh sách chầu rìa thông tin mới
             */
            var channelService = me.app.get('channelService');
            var channel = channelService.getChannel(tid, true);

            /**
             * Trường hợp mặc định là state = JOIN
             * - Người nhận messages gồm:
             * + Bản thân người ngồi
             * + Danh sách người chơi đang ngồi
             * + Danh sách chầu rìa
             */
            // Danh sách người chơi đang ngồi trong bàn
            var listJoinGame    = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var listMembers     = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var listReceiver    = []; // lưu danh sách người nhận message (uid, sid) => TH JOIN
            listReceiver        = me.getReceiver(listJoinGame, channel);
            var waitingsPlayer  = []; // TH bàn chưa chơi thì = rỗng

            var listNotEnough       = me.getPlayersJSON(tid, 'playersToNotEnough', uid);
            var notEnoughReceiver   = me.getReceiver(listNotEnough, channel);

            //listReceiver = _.difference(listReceiver, notEnoughReceiver);

            // lay danh sach gameWinners
            //var listWinners = me.getPlayersJSON(tid, 'gameWinners', uid);

            logger.info("listJoinGame: ", listJoinGame, " => uid: ", uid);
            //logger.info("me.tables[tid].state: ", me.tables[tid]);

            let listPlayers = [];
            if (me.tables[tid].state === "IN_PROGRESS" || me.tables[tid].state === "END_GAME") {
                // TH: state = IN_PROGRESS
                /**
                 * Bàn đang chơi trả về danh sách người đang chơi + người đang ngồi
                 * -> push về cho chính người vừa ngồi xuống
                 */
                listPlayers = me.getPlayersJSON(tid, 'players', uid);
                listMembers     = _.union(listMembers, listPlayers);

                if (typeof channel.getMember(uid) !== "undefined"){
                    var receiverTmp = {uid: uid, sid: channel.getMember(uid)['sid']};

                    listReceiver.push(receiverTmp);
                }

                if (me.tables[tid].state === "IN_PROGRESS")
                    waitingsPlayer  = me.getPlayersJSON(tid, 'playersToAdd', uid);

                if (me.tables[tid].state === "END_GAME")
                    waitingsPlayer  = []; //me.getPlayersJSON(tid, 'playersToAdd', uid);
            }

            listMembers     = _.uniq(listMembers);
            listReceiver    = _.uniq(listReceiver);

            logger.info("listReceiver: ", listReceiver);
            logger.info("waitingsPlayer: ", waitingsPlayer);

            channelService.pushMessageByUids({
                route  : consts.GAME.ROUTER.JOIN_GAME, //'onTableJoin',
                members : listMembers,
                players : me.getPlayersJSON(tid, 'players', uid), // Thêm danh sách người đang chơi
                waitings: waitingsPlayer,
                state: me.tables[tid].state, // trạng thái của bàn
                board: me.getTableJSON(tid, uid)?.board ?? [],
                currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
                currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
                timestamp: Math.floor(Date.now() / 1000) // tạm để
            }, listReceiver, function(e) {
                if(e) {
                    logger.error(consts.GAME.ROUTER.JOIN_GAME, ': unable to push members ', e);
                }
                // begin: lấy danh sách người chơi chầu rìa
                //var members         = table.members; // danh sách người chơi có mặt trong bàn
                //var lobbyListTemp   = me.getDanhSachChauRia(members, listMembers);

                logger.info("table: " , table);
                logger.info("table.members: " , table.members);
                logger.info("listMembers: " , listMembers);
                var chauria = me.getChauRia(table.members, listMembers, channel);

                // bỏ qua những player trong nhóm không đủ tiền
                // chauria = _.difference(chauria, notEnoughReceiver);

                // Gộp 2 mảng lại với nhau
                var listAll = _.union(chauria, notEnoughReceiver);
                // Bỏ các phần tử trong nhóm không đủ tiền ra khỏi danh sách đc nhận message
                chauria = _.filter(listAll, function(obj){ return !_.findWhere(notEnoughReceiver, obj); });

                // Bắn dữ liệu về cho danh sách đang chầu rìa
                logger.info("uid: " , uid, " -> ban cho nhung nguoi chau ria3:", chauria);

                logger.info("1.All member: ", table.members);
                logger.info("1.listMembers: ", listMembers);
                logger.info("1.chauria: ", chauria);

                if (chauria.length > 0) {
                    logger.info("-> so nguoi chau ria(", chauria.length ,") > 0 -> pushMessage");
                    channelService.pushMessageByUids({
                        route: consts.GAME.ROUTER.JOIN_GAME,
                        members: listMembers,
                        players: listPlayers,
                        msg: me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid),
                        waitings: waitingsPlayer,
                        state: me.tables[tid].state, // trạng thái của bàn
                        board: me.getTableJSON(tid, uid)?.board ?? [],
                        timestamp: Math.floor(Date.now() / 1000), // tạm để
                        currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
                        currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
                    }, chauria, function (e) {
                        logger.info("-> callback sau khi pushMessage cho chau ria");
                    });
                }

            });

            // check nếu số người chơi > 2 thì auto start game
            // --------------------------------------------------------------------------------------
            var _listPlayerToAdd = me.getPlayersJSON(tid, 'playersToAdd', uid);
            var _playersToAddLength = _listPlayerToAdd.length;
            logger.info(" --> So Nguoi choi ngoi xuong ghe: " + _playersToAddLength);
            if(_playersToAddLength >= 2) {
                logger.info("So nguoi > 2 roi kia : ", _playersToAddLength, " add to jobStartGame");
                //me.app.rpc.game.tableRemote.addJobStartGame(null, tid, consts.GAME.TIMER.FIRST_START ,function () {});
                me.app.rpc.game.tableRemote.addJobStartGame(null, tid, timerDefault ,function () {});
            }

            // set chế độ đang không hoạt động
            // -----------------------------------------------------------------------------------------------------------
            table.isSitDown = false;
            logger.info("[end] addPlayer uid ", uid, " đã ngồi xong with actorNr:", index, " and isSitDown: ", table.isSitDown);
            cb();
        });
    });
};

/*
TableService.prototype.pushMessageByUidsInJoinGame = function (tid, arrReceiver, dataSend) {
    var channelService = me.app.get('channelService');
    var channel = channelService.getChannel(tid, true);

    channelService.pushMessageByUids({
        route:consts.GAME.ROUTER.JOIN_GAME,
        members:dataSend,
        msg: dataSend
    }, arrReceiver, null);

};
*/

/**
 * Kiểm tra uid có nằm trong
 * @param table
 * @param uid
 * @return {boolean}
 */
var checkUserInToPlayersToAdd = function (table, uid){
    return _.findWhere(table.playersToAdd, {id: uid})
};

/**
 * Kiểm tra xem uid có đang chơi game hay không
 * @param table
 * @param uid
 */
TableService.prototype.checkUserInToPlayers = function (table, uid){
    return _.findWhere(table.players, {id: uid})
};


TableService.prototype.getChauRia = function (arr1, arr2, channel) {
    logger.info("app||services||tableService||getChauRia => arr1: ", arr1);
    logger.info("app||services||tableService||getChauRia => arr2: ", arr2);

    var _newArr1, _newArr2;
    _newArr1 = [];
    for(var i = 0; i<arr1.length; i++) {
        _newArr1.push(arr1[i]["id"]);
    }

    _newArr2 = [];
    for(var j = 0;j <arr2.length; j++) {
        _newArr2.push(arr2[j]["id"]);
    }

    logger.info("app||services||tableService||getChauRia => _newArr1: ", _newArr1);
    logger.info("app||services||tableService||getChauRia => _newArr2: ", _newArr2);

    var lobbyList = _.difference(_newArr1, _newArr2);
    logger.info("app||services||tableService => lobbyList: ", lobbyList);
    var chauria = [];
    for(var m = 0; m < lobbyList.length; m++) {
        //var _sid = channel.getMember(lobbyList[m])['sid'];
        var gMb = channel.getMember(lobbyList[m]);
        if (gMb["sid"] !== undefined){
            logger.info("sid of ", lobbyList[m] , " is ", gMb["sid"]);
            var _sid = gMb["sid"];
            var arrTemp = {uid: lobbyList[m], sid: _sid};
            chauria.push(arrTemp);
        }
    }
    logger.info("app||services||tableService => chauria: ", chauria);
    return chauria;
};

TableService.prototype.getReceiver = function (arrList, channel) {

    var _newArr1, dataReturn, i, m;
    _newArr1 = [];
    for(i = 0; i<arrList.length; i++) {
        _newArr1.push(arrList[i]["id"]);
    }

    dataReturn = [];

    for(m = 0; m < _newArr1.length; m++) {
        logger.info("[getReceiver] -> _newArr1[", m ,"]: ", _newArr1[m], " channel.getMember(_newArr1[m]): ", channel.getMember(_newArr1[m]));
        if (typeof channel.getMember(_newArr1[m]) !== "undefined") {
            // var arrTemp = {uid: _newArr1[m], sid: channel.getMember(_newArr1[m])['sid']};
            // dataReturn.push(arrTemp);
            dataReturn.push(channel.getMember(_newArr1[m]));
        }
    }
    logger.info("[getReceiver] >> dataReturn: ", dataReturn);
    return dataReturn;
};

TableService.prototype.getDanhSachChauRia = function (arr1, arr2) {
    var _newArr1, _newArr2;
    _newArr1 = [];
    for(var i = 0; i<arr1.length; i++) {
        _newArr1.push(arr1[i]["id"]);
    }

    _newArr2 = [];
    for(var j = 0;j <arr2.length; j++) {
        _newArr2.push(arr2[j]["id"]);
    }

    var lobbyList = _.difference(_newArr1, _newArr2);

    logger.info("_newArr1: ", _newArr1);
    logger.info("_newArr2: ", _newArr2);
    logger.info("lobbyList: ", lobbyList);

    return lobbyList;
};


/**
 * Push detailed user information to a user
 *
 * @param {Object} tid id of an existing table
 * @param {string} uid userId to add to the table
 * @param {object} info player information
 * @param {function} cb callback
 *
 */
TableService.prototype.pushPlayerInfo = function(tid, uid, cb) {
    logger.info("[tableService.pushPlayerInfo] tid: ", tid, " => uid: ", uid);
    var me = this;
    //me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (info) {
    // userDao.getPlayerByUid(uid, function (e, info) {
    me.app.rpc.db.dbRemote.getPlayerById('*', uid, async (e, code, info) => {
        logger.info("[tableService.pushPlayerInfo] e:", e, " => code: ", code, ' => info: ', info);

        // let balance = 0;
        // if (code === CODE.OK) {
        //     // lấy thông tin tiền từ grpc payment
        //     // ------------------------------------------------------------------------------------------------
        //     const idOfUser = uid;
        //     const { codeBalance, balanceRes } = await new Promise((resolve, reject) => {
        //         me.app.rpc.coin.coinRemote.getAccountInfo('*', { userId: idOfUser }, (err, codeBalance, balanceRes) => {
        //             if (err) reject(err);
        //             else resolve({ codeBalance, balanceRes });
        //         });
        //     });
        //     logger.info('pushPlayerInfo >> codeBalance', codeBalance, ' -> balanceRes: ', balanceRes);

        //     if (Number(codeBalance) === 200) {
        //         balance = balanceRes?.balance ?? 0;
        //     }
        // }
        // info.balance = balance;

        var channelService = me.app.get('channelService');
        var channel = channelService.getChannel(tid, false);
        if (!channel || !channel.getMember(uid)) return;
        channelService.pushMessageByUids({
            route: consts.GAME.ROUTER.UPDATE_MYSELF,
            //user: info
            msg: info
        }, [{
            uid: uid,
            sid: channel.getMember(uid)['sid']
        }], function (e) {
            if (e) {
                logger.error('[tableService.pushPlayerInfo] unable to push player info ', e, ' -> message: ', e.message, ' -> stack: ', e.stack);
            }
            logger.info("[tableService.pushPlayerInfo] done [ok]");
            cb("done");
        });

    });
};


TableService.prototype.checkAndUpdateDataPlayer = function (info) {
    logger.info("[checkAndUpdateDataPlayer] info: ", info);
    var self = this;
    const balance = info?.balance ?? 0;
    logger.info("[checkAndUpdateDataPlayer] balance: ", balance);
    if (balance < 0) {
        logger.info("[checkAndUpdateDataPlayer] Truờng hợp số tiền < 0 ", info);
        info.balance    = 0;
        // var dataUpdate  = {
        //     balance: 0
        // };
        // userDao.updatePlayer(info.uid, dataUpdate, function (e, res) {
        //     logger.info("[checkAndUpdateDataPlayer] Số tiền đã được cập nhật về 0 với res: ", res, " => e ", e);
        // });
        const userId = info?.id ?? 0; // info?.user_id ?? '';
        var dataUpdate  = {
            // userId: info.uid,
            // userId: userId,
            // amount: 0,
            // description: '',
            balance: 0
        };
        // self.app.rpc.coin.coinRemote.cashIn('*', dataUpdate, function (res) {
        self.app.rpc.db.dbRemote.updatePlayer('*', userId, dataUpdate, function (err, code, res) {
            logger.info("[checkAndUpdateDataPlayer] Số tiền đã được cập nhật về 0 với res: ", res, ' -> err: ', err, ' -> code: ', code);
        });

        // Cập nhật lại dữ liệu trong cached
        // --------------------------------------------------------------------------------------
        // self.app.rpc.manager.userRemote.onUpdatePlayer(null, info.uid, info, function (user) {
        self.app.rpc.manager.userRemote.onUpdatePlayer(null, userId, info, function (user) {
            //logger.info("thong tin user from useCached sau khi updatePlayer: ", user);
        });

    }
};

TableService.prototype.pushPlayerInfo2 = function(tid, uid, channelService, channel, cb) {
    logger.info("[tableService.pushPlayerInfo2] tid: ", tid, " -> uid: ", uid);

    var self = this;
    // userDao.getPlayerByUid(uid, function (e, info) {
    self.app.rpc.db.dbRemote.getPlayerById('*', uid, async (e, code, info) => {
        logger.info("[tableService.pushPlayerInfo2] e: ", e, " -> code: ", code, ' -> info: ', info);

        // let balance = 0;
        // if (code === CODE.OK) {
        //     // lấy thông tin tiền từ grpc payment
        //     // ------------------------------------------------------------------------------------------------
        //     const idOfUser = uid;
        //     const { codeBalance, balanceRes } = await new Promise((resolve, reject) => {
        //         self.app.rpc.coin.coinRemote.getAccountInfo('*', { userId: idOfUser }, (err, codeBalance, balanceRes) => {
        //             if (err) reject(err);
        //             else resolve({ codeBalance, balanceRes });
        //         });
        //     });
        //     logger.info('[tableService.pushPlayerInfo] >> codeBalance', codeBalance, ' -> balanceRes: ', balanceRes);

        //     if (Number(codeBalance) === 200) {
        //         balance = balanceRes?.balance ?? 0;
        //     }
        // }
        // info.balance = balance;

        // - Check số tiền hiện tại < 0 thì cập nhật lại về 0 và return về cho client
        // ------------------------------------------------------------------------------------------
        self.checkAndUpdateDataPlayer(info);

        // Lấy thông tin và push về cho chính client đó
        // ------------------------------------------------------------------------------------------
        if(!channel || !channel.getMember(uid)) return;

        channelService.pushMessageByUids({
            route: consts.GAME.ROUTER.UPDATE_MYSELF,
            msg: info
        }, [{
            uid: uid,
            sid: channel.getMember(uid)['sid']
        }], function (e) {
            if (e) {
                logger.error('[tableService.pushPlayerInfo2] unable to push player info ', e, ' -> message: ', e.message, ' -> stack: ', e.stack);
            }
            cb("done");
        });

    });

};

/**
 * Start the game
 *
 * @param {Object} tid id of an existing table
 * @param {function} cb callback
 *
 */
TableService.prototype.startGame = function(tid, cb) {
    var table = this.tables[tid];
    if(!table){
        return cb('table-not-found');
    }
    //if(table.state != 'JOIN'){
    logger.info("[startGame] table.state: ", table.state);
    //if(table.state != 'JOIN' || table.state != 'END_GAME'){
    if(table.state == 'IN_PROGRESS'){
        return cb('table-not-ready');
    }
    logger.info('[startGame] table.table.active: ', table.table.active);
    if(table.table.active){
        return cb('table-still-active');
    }
    logger.info('[startGame] table.table.playersToAdd.length: ', table.table.playersToAdd.length);
    if(table.table.playersToAdd.length < table.table.minPlayers) {
        logger.info('[startGame] table.table.playersToAdd: ', table.table.playersToAdd);
        return cb('not-enough-players');
    }
    if(table.table.playersToAdd.length > table.table.maxPlayers){
        return cb('too-many-players');
    }
    // remove chips from user for buy in
    table.table.StartGame();
    this.app.get('channelService').getChannel(tid, true).pushMessage({
        route   : 'onUpdateUsers',
        members : table.table.members
    });
    this.broadcastGameState(tid);
    cb();
};

/**
 * Perform a game action
 *
 * @param {string} tid table id
 * @param {string} uid userId to add to the table
 * @param {object} action an object containing the action type and optionally the amount of chips
 * @param {function} cb callback
 *
 */
TableService.prototype.performAction = function(tid, uid, action, cb){
    logger.info("performAction >> tid: ", tid, " -> uid: ", uid, " -> action: ", action);
    var me = this;
    var table = this.tables[tid];

    if(!table){
        return cb('table-not-found');
    }
    if(table.state != 'IN_PROGRESS'){
        return cb('game-not-ready');
    }
    if(me.getPlayerIndex(tid, uid) != table.table.currentPlayer){
        return cb('not-your-turn');
    }
    if(me.getPlayerJSON(tid, uid).folded == true){
        return cb('already-folded');
    }
    if(action.action == 'bet' && isNaN(action.amt)){
        return cb('invalid-bet-amt');
    }

    // check thêm trạng thái có đang xử lý action nào không, tránh việc xử lý liên tiếp
    if(table.table.isAction){
        logger.info("Đang xử lý action : ", action.action);
        return cb("action-is-processing");
    }
    // bắt đầu set trạng thái đang hoạt động
    // ------------------------------------------------------------------------------------------
    table.table.isAction = true;
    //logger.debug("app||services||tableService: in tables: ", table.table);

    // perform action
    if(action.action == 'call'){
        table.table.players[table.table.currentPlayer].Call();
    }else if(action.action == 'bet'){
        table.table.players[table.table.currentPlayer].Bet(parseInt(action.amt));
        // fire emit event eventPlayerBet (phần nhiệm vụ)
        me.app.event.emit('eventPlayerBet', {
            playerId: uid,
            betAmount: parseInt(action.amt)
        });
    }else if(action.action == 'check'){
        table.table.players[table.table.currentPlayer].Check();
    }else if(action.action == 'allin'){
        table.table.players[table.table.currentPlayer].AllIn();
    }else if(action.action == 'fold'){
        table.table.players[table.table.currentPlayer].Fold();
    }else{

        table.table.isAction = false;

        return cb('invalid-action');
    }
    table.table.stopTimer();
    // chuyển trạng thái của isAction = false không hoạt động
    // ------------------------------------------------------------------------------------------
    table.table.isAction = false;

    logger.debug('player ' + uid + ' executed action ' + action.action + ' on table ' + tid + ' with state ' + table.state);

    me.handleGameState(tid, function(e) {
        if(e){
            return cb(e);
        }
        cb();
    });
};

/**
 * End game and broadcast result to clients
 *
 * @param {string} tid table id
 * @param {function} cb callback
 *
 */
TableService.prototype.endGame = function(tid, cb) {
    logger.info("[tableService.endGame] tid: ", tid);
    var me = this;
    if(!me.tables[tid]){
        cb('table-not-found');
        return;
    }
    var table = me.tables[tid];
    if(table.table.game.roundName != 'GameEnd'){
        cb('not-game-end');
        return;
    }
    table.table.active = false;
    table.table.stopTimer();
    me.saveResults(tid, function(e){
        if(e){
            cb(e);
            return;
        }
        var channelService = me.app.get('channelService');
        channelService.getChannel(tid, false).pushMessage({
            route   : 'onUpdateUsers',
            members : table.table.members
        });

        // init new game
        table.table.initNewGame("END_GAME");

        setTimeout(function() {

            logger.info("[tableService.endGame] đợi hết timeout để send command endGame xuống client");

            me.broadcastGameState(tid);

            // begin: push command for client clear table
            // --------------------------------------------------------------------------------------------------
            logger.info("[tableServices] -> add Job send command clear addJobClearGame -----------------");
            me.app.rpc.game.tableRemote.addJobClearGame(null, tid, consts.GAME.TIMER.WAIT_CLEAR ,function () {});


            // begin: check time để auto start - 8s
            // --------------------------------------------------------------------------------------------------
            logger.info("[tableServices] -> add Job addJobStartGame tid: ", tid, " -> consts.GAME.TIMER.WAIT_START: ", consts.GAME.TIMER.WAIT_START);
            me.app.rpc.game.tableRemote.addJobStartGame(null, tid, consts.GAME.TIMER.WAIT_START ,function () {});

        }, 2500);

        /*
        me.broadcastGameState(tid);

        // in thử danh sách cached xem đã cập nhật lại kết quả chưa
        // --------------------------------------------------------------------------------------------------
        // me.app.rpc.manager.userRemote.getUsersCache(null, function (res) {
        //     logger.info("userCache sau khi ket thuc van update lai: ", res);
        // });

        // begin: push command for client clear table
        // --------------------------------------------------------------------------------------------------
        logger.info("[tableServices] -> add Job send command clear addJobClearGame -----------------");
        me.app.rpc.game.tableRemote.addJobClearGame(null, tid, consts.GAME.TIMER.WAIT_CLEAR ,function () {});


        // begin: check time để auto start - 8s
        // --------------------------------------------------------------------------------------------------
        logger.info("[tableServices] -> add Job addJobStartGame -----------------");
        me.app.rpc.game.tableRemote.addJobStartGame(null, tid, consts.GAME.TIMER.WAIT_START ,function () {});
        */

        cb();
    });
};


/**
 *
 * - push command clear hết dữ liệu từ ván trước để chuẩn bị start ván mới
 * - sử dụng trước khi bắt đầu ván mới
 * @param tid
 */
TableService.prototype.pushTableClear = function (tid, cb){
    logger.info("[pushTableClear] send command clear data to table ", tid);
    var i               = 0;
    var me              = this;
    var channelService  = me.app.get('channelService');
    var channel         = channelService.getChannel(tid, false);

    // udpate check table not found
    if(me.tables[tid]) {
        // check player at 0 position not undefined
        if (me.tables[tid].table.members[i] !== undefined) {
            var uid         = me.tables[tid].table.members[i].id;
            var receives    = me.getPlayersJSON(tid, 'playersToAdd', uid);

            if (receives.length <= 0){
                receives = me.getPlayersJSON(tid, 'players', uid);
            }

            logger.info("[pushTableClear] Receives: ", receives);

            // logger.info("[pushTableClear] getTableJSON: ", me.getTableJSON(tid, uid));

            if (channel.getMember(uid)) {
                me.app.get('channelService').getChannel(tid, false).pushMessage({
                    route: consts.GAME.ROUTER.GAME_CLEAR, //'onClearTable',
                    members: me.getPlayersJSON(tid, 'playersToAdd', uid)
                    //msg: me.getPlayersJSON(tid, 'playersToAdd', uid)
                });
            }
        }// end check player with i = 0 not undefined
    }
    cb();
};

/**
 * Push danh sách người chơi về client onTablesJoin
 * @param tid
 */
TableService.prototype.initTableJoin = function (tid){
    var me = this;
    if(!this.tables[tid]){
        cb('table-not-found');
    }
    // var members_length = me.tables[tid].table.members.length;

    var i = 0;

    //for (var i = 0; i< members_length; i++) {
        var uid = me.tables[tid].table.members[i].id;

        if (me.tables[tid].state === "IN_PROGRESS")
            waitingsPlayer  = me.getPlayersJSON(tid, 'playersToAdd', uid);

        if (me.tables[tid].state === "END_GAME")
            waitingsPlayer  = [];

        me.app.get('channelService').getChannel(tid, false).pushMessage({
            route  : consts.GAME.ROUTER.JOIN_GAME, //'onTableJoin',
            msg    : me.getPlayerJSON(tid, uid, 'playersToAdd') || me.getPlayerJSON(tid, uid),
            members: me.getPlayersJSON(tid, 'playersToAdd', uid),
            players: me.getPlayersJSON(tid, 'players', uid), // Thêm danh sách người đang chơi
            waitings: waitingsPlayer,
            state: me.tables[tid].state, // trạng thái của bàn
            board: me.getTableJSON(tid, uid)?.board ?? [],
            timestamp: Math.floor(Date.now() / 1000), // tạm để
            currentPlayerTime: me.getTableJSON(tid, uid)?.currentPlayerTime ?? 0,
            currentPlayerIndex: me.getTableJSON(tid, uid)?.currentPlayer ?? null,
        });
    //}
};

/**
 * Store table results to persistence
 *
 * @param {string} tid id of the table
 * @param {string} cb callback
 *
 */
/*
TableService.prototype.saveResults = function(tid, cb){
    var me = this;
    if(!this.tables[tid]){
        cb('table-not-found');
    }
    var table = this.tables[tid];
    TableStore.getByAttr('id', table.table.game.id, function(e, foundTable){
        if(foundTable){
            cb('game-already-exists');
            return;
        }
        TableStore.create(me.getTableJSON(tid), function(e, newTable){
            if(e){
                cb(e);
                return;
            }
            var i = 0;
            function saveWinner(){
                me.updatePlayerInfo(table.table.gameWinners[i].id, {
                    wins      : 1,
                    wonAmount : table.table.gameWinners[i].amount
                }, function(){
                    if(++i === table.table.gameWinners.length){
                        cb();
                    }else{
                        saveWinner();
                    }
                });
            }
            if(table.table.gameWinners.length){
                saveWinner();
            }else{
                return cb();
            }
        });
    });
};
*/
TableService.prototype.saveResults = async function(tid, cb) {

    var me = this;
    let lid = 0;
    try {

        if(!this.tables[tid]) {
            cb('table-not-found');
        }
        var table       = this.tables[tid];
        var tableInfo   = me.getTableJSON(tid);
        var users       = tableInfo.players;

        logger.info("[tableService.saveResults] tableInfo: ", tableInfo);
        logger.info("[tableService.saveResults] users: ", users);
        if (tableInfo.gameWinners.length > 0) {

            // Begin: Push log to rabbitmq
            // ---------------------------------------------------------------------------------------------------
            // const logsPlayer = users.map(user => user.id);
            const userIds = users.map(user => user.id);
            logger.info("[tableService.saveResults] userIds: ", userIds);
            const logsGamePayload = {
                logs: utils.jsonEndcode(tableInfo).toString(),
                players: userIds // logsPlayer,
            }
            logger.info("[tableService.saveResults] logsGamePayload: ", logsGamePayload);

            const rabbitmqService = me.app.get('rabbitmqService');
            try {
                // Kiểm tra kết nối
                if (!rabbitmqService.channel || !rabbitmqService.connection) {
                    await rabbitmqService.connect();
                }

                await rabbitmqService.sendToQueue(
                    consts.GAME.LOGS.EX,
                    utils.jsonEndcode(logsGamePayload).toString()
                );
                logger.info("[tableService.saveResults] Sent log to RabbitMQ successfully");
            } catch (rabbitErr) {
                logger.error("[tableService.saveResults] Failed to send log to RabbitMQ: ", rabbitErr);
                // Implement backup solution (store to DB or file)
                // await storeLogToBackup(logsGamePayload);
            }
            // End: Push log to rabbitmq
            // ---------------------------------------------------------------------------------------------------

            var expService = this.app.get('expService');
            let beforePlayersDetails = [];

            async.waterfall([
                function (callback) {
                    // Step 0: Get players information by UIDs before updating logs
                    // ----------------------------------------------------------------------------------------------
                    // const userIds = users.map(user => user.id);
                    logger.info("[tableService.saveResults][Step 0] Getting player info for UIDs: ", userIds);
                    me.app.rpc.db.dbRemote.getPlayersByIds('*', userIds, (err0, code0, playersList) => {
                        if (err0) {
                            logger.error("[tableService.saveResults] Failed to get players by IDs: ", err0);
                            return callback(err0);
                        }

                        logger.info("[tableService.saveResults] Retrieved players info: ", playersList);

                        // Add player information to the table info for more detailed logging
                        if (playersList && Array.isArray(playersList)) {
                            // tableInfo.playersDetails = playersList;
                            beforePlayersDetails = playersList;
                        }

                        callback(null);
                    });
                },
                function (callback) {
                    // Step 1: Ghi Logs vào database
                    // ----------------------------------------------------------------------------------------------
                    // logger.info("[tableService.saveResults] tableInfo: ", tableInfo);
                    // logsGameDao.createLogs(users, tableInfo, callback);
                    me.app.rpc.db.dbRemote.createLogs('*', users, tableInfo, callback);
                },
                // function (users, callback) {
                function (code1, dataCb, callback) {
                    logger.info("[tableService.saveResults][Step 2] users sau khi đã ghi log: ", dataCb, ' -> code1: ', code1);
                    // Step 2: Lấy thông tin users vừa ghi log và cập nhật lại tiền ở step 1
                    // ----------------------------------------------------------------------------------------------
                    var listUid = dataCb?.data ?? []; // đây là mảng uids = [1, 2] => tương đồng với userIds ở trên
                    lid = dataCb?.lid ?? 0;

                    // bắn sự kiện end game cho các event listener đang đăng ký: transactions, missions
                    // ----------------------------------------------------------------------------------------------
                    me.app.event.emit('endGame', {
                        logs: tableInfo,
                        lid: lid,
                        before_players: beforePlayersDetails
                    });

                    // Lấy lại thông tin danh sách players sau khi cập nhật lại tiền từ database
                    // ----------------------------------------------------------------------------------------------
                    logger.info("[tableService.saveResults] listUid: ", listUid);
                    // userDao.getPlayerByUids(joinUids, function (e, res) {
                    //     if (res != null) {
                    //         callback(null, joinUids, res);
                    //     }
                    // });
                    me.app.rpc.db.dbRemote.getPlayersByIds('*', listUid, (e2, code2, res) => {
                        logger.info("[tableService.saveResults] getPlayersByIds >> e2: ", e2, ' -> code2: ', code2, ' -> res: ', res);
                        callback(null, listUid, res);
                    });
                },
                function (uids, usersCb, callback) {
                    logger.info("[tableService.saveResults][Step 3] uids: ", uids, " => usersCb: ", usersCb);
                    // [OLD] Step 3: Lấy thông tin properties theo list uids
                    // ----------------------------------------------------------------------------------------------
                    // userDao.getPropertiesByUids(uids, function (e, res) {
                    //     logger.info("Thông tin users sau khi đã cập nhật thông tin: uids4444: ", uids, " => res: ", res);
                    //     if (res != null) {
                    //         var arrRes = {
                    //             users: users,
                    //             properties: res
                    //         };
                    //         // cập nhật lại 1 số thuộc tính của người thắng: số tiền thắng , bộ bài
                    //         me.__updatePlayJson(res, tableInfo, function (e, _res) {
                    //             //callback(null, joinUids, res);
                    //         });
                    //         callback(null, arrRes);
                    //     }
                    // });

                    // me.app.rpc.db.dbRemote.getPropertiesByIds('*', uids, (e3, code3, res) => {
                    //     logger.info("[tableService.saveResults] getPropertiesByIds >> e3: ", e3, ' -> code3: ', code3, ' -> res: ', res);

                    //     if (!e3) {
                    //         var arrRes = {
                    //             users: users,
                    //             properties: res
                    //         };
                    //         // cập nhật lại 1 số thuộc tính của người thắng: số tiền thắng , bộ bài
                    //         me.__updatePlayJson(res, tableInfo, function (e, _res) {
                    //             //callback(null, joinUids, res);
                    //         });
                    //         logger.info("[tableService.saveResults] arrRes: ", arrRes);
                    //         callback(null, arrRes);
                    //     }

                    // });

                    if (usersCb && usersCb.length > 0) {
                        // var arrRes = {
                        //     users: users,
                        //     properties: res
                        // };
                        // cập nhật lại 1 số thuộc tính của người thắng: số tiền thắng , bộ bài
                        me.__updatePlayJson(usersCb, tableInfo, function (e, _res) {
                            //callback(null, joinUids, res);
                            callback(null, usersCb);
                        });
                        // logger.info("[tableService.saveResults] arrRes: ", arrRes);
                        // logger.info("[tableService.saveResults] users: ", users);
                        // callback(null, arrRes);
                        // callback(null, users);
                    }
                },
                // function (result, callback) {
                //     logger.info("[tableService.saveResults] Step 4: result: ", result);
                //     // Step 4: Cập nhật lại properties vào từng user tương ứng
                //     // ----------------------------------------------------------------------------------------------
                //     // var users = result.users;
                //     // var properties = result.properties;
                //     // var arr = [];
                //     // for (var i = 0; i < users.length; i += 1) {
                //     //     var user = users[i];
                //     //     logger.info("[tableService.saveResults] Step 4: result >> user: ", user);
                //     //     // var proByUid = _.findWhere(properties, {uid: user.uid});
                //     //     var proByUid = _.findWhere(properties, {id: user.id});
                //     //     user.username = user.nick_name;
                //     //     user.full_name = user?.display_name ?? '';
                //     //     user.properties = proByUid;
                //     //     arr.push(user);
                //     // }
                //     // callback(null, arr);

                //     var users = result;
                //     // var properties = result.properties;
                //     var arr = [];
                //     for (var i = 0; i < users.length; i += 1) {
                //         var user = users[i];
                //         logger.info("[tableService.saveResults] Step 4: result >> user: ", user);
                //         // var proByUid = _.findWhere(properties, {uid: user.uid});
                //         var proByUid = _.findWhere(properties, {id: user.id});
                //         user.username = user.nick_name;
                //         user.full_name = user?.display_name ?? '';
                //         user.properties = proByUid;
                //         arr.push(user);
                //     }
                //     callback(null, arr);
                // },
                function (arr, callback) {
                    logger.info("[tableService.saveResults][Step 5] arr: ", arr);
                    // Step 5: Cập nhật lại info vào userCached
                    // ----------------------------------------------------------------------------------------------
                    // const playerCount = arr.length || 0;
                    for (var k = 0; k < arr.length; k += 1) {
                        var user = arr[k];
                        logger.info("[tableService.saveResults][Step 5] >> user: ", user);
                        // me.app.rpc.manager.userRemote.onUpdatePlayer(null, user.uid, user, function (user) {
                        me.app.rpc.manager.userRemote.onUpdatePlayer(null, user.id, user, function (user) {
                            //logger.info("thong tin user from useCached sau khi updatePlayer: ", user);
                        });
                    }
                    callback(null, "done");
                }
            ], function (err, result) {
                logger.info("[tableService.saveResults] in saveResults err: ",err," => result: ", result);
                /*
                    me.app.rpc.manager.userRemote.getUsersCache(null, function (res) {
                        logger.info("userCache sau khi ket thuc van update lai: ", res);
                    });
                */

                // Begin: tính toán điểm kinh nghiệm
                // -------------------------------------------------------------------------------------------------------------------------------------
                const players = tableInfo.gameWinners[0]?.players || [];
                const totalPlayer = players.length || 0;

                for(let i = 0; i < users.length; i += 1) {
                    const user = users[i];
                    const uid = parseInt(user.id, 10);

                    // Tìm thông tin người chơi trong danh sách người thắng
                    const playerData = _.findWhere(players, {id: uid});
                    const isWin = playerData ? playerData.isWinner : false;

                    if (logger.isInfoEnabled()) {
                        logger.info(`[tableService.saveResults] Tính điểm EXP cho user ${uid}, isWin: ${isWin}, totalPlayer: ${totalPlayer}`);
                    }

                    // Gọi service tính điểm kinh nghiệm
                    expService.calculateGameResultExp({
                        playerId: uid,
                        isWin: isWin,
                        playerCount: totalPlayer,
                        smallBlind: tableInfo.smallBlind,
                        playerLevel: user.level
                    }, function (err, code, result) {
                        logger.info("[tableService.saveResults] calculateGameResultExp err: ", err, " => code: ", code, " => result: ", result);
                        if (err) {
                            logger.error(`[tableService.saveResults] Lỗi khi tính điểm EXP cho user ${uid}:`, err);
                            return;
                        }

                        if (logger.isDebugEnabled()) {
                            logger.debug(`[tableService.saveResults] User ${uid} ${isWin ? 'thắng' : 'thua'}, nhận được ${result?.expValue || result?.expAdded} điểm EXP`);
                        }
                    });
                }
                // -------------------------------------------------------------------------------------------------------------------------------------
                // End: tính toán điểm kinh nghiệm

                return cb();
            });

        }else{
            return cb();
        }

        // Add logs to mysql database
        // ---------------------------------------------------------------------------------------------------
        /*
        logsGameDao.createLogs(users, tableInfo, function(e, res){

            if(e){
                cb(e);
                return;
            }
            var i = 0;
            function saveWinner(){

                me.updatePlayerInfo(table.table.gameWinners[i].id, {
                    wins      : 1,
                    wonAmount : table.table.gameWinners[i].amount,
                    gameId: tid,
                    isPlayer: true // trang thai van dang choi game
                }, function(){
                    if(++i === table.table.gameWinners.length){
                        cb();
                    }else{
                        saveWinner();
                    }
                });
            }
            if(table.table.gameWinners.length){
                saveWinner();
            }else{
                return cb();
            }
        });
        */

    } catch (err) {
        logger.error("[tableService.saveResults] Error:", err);
        return cb(err);
    }
};

TableService.prototype.__updatePlayJson = function (res, tableInfo, cb) {
    var gameWinner, uidWinner, playJson, playerList, winnerPlayer, amountWin, cardHands, code;
    gameWinner      = tableInfo.gameWinners;
    uidWinner       = gameWinner[0].id;
    playerList      = gameWinner[0].players;
    winnerPlayer    = _.findWhere(playerList, {id: uidWinner});

    logger.info("[tableService.__updatePlayJson] tableInfo>>gameWinner with uid ", uidWinner ," >> gameWinner", gameWinner);
    logger.info("[tableService.__updatePlayJson] tableInfo>>gameWinner>>playerList: ", playerList);
    logger.info("[tableService.__updatePlayJson] tableInfo>>gameWinner>>playerList>>winnerPlayer: ", winnerPlayer);

    amountWin = winnerPlayer.amount;
    cardHands = winnerPlayer.hand.cards;
    code      = winnerPlayer.hand.code;

    logger.info("[tableService.__updatePlayJson] tableInfo>>res: ", res);

    // var userWinner = _.findWhere(res, {uid: uidWinner});
    var userWinner = _.findWhere(res, {id: uidWinner});

    var biggestWon, bestHand, codeWin;

    logger.info("[tableService.__updatePlayJson] tableInfo>>userWinner: ", userWinner);

    // playJson = userWinner.playJson;
    // playJson = userWinner?.play_json;
    playJson = userWinner?.properties?.play_json || {};

    playJson = JSON.parse(playJson);
    logger.info("[tableService.__updatePlayJson] tableInfo>>userWinner>>playJson: ", playJson);

    //logger.info("__updatePlayJson >> typeof playJson: ", utils.empty(playJson));
    logger.info("[tableService.__updatePlayJson] typeof playJson.biggestWon: ", typeof playJson.biggestWon);
    logger.info("[tableService.__updatePlayJson] playJson.biggestWon: ", playJson.biggestWon);

    //if (playJson != null){
    //if (typeof playJson.biggestWon !== "undefined" || playJson != null){
    if (typeof playJson.biggestWon != "undefined"){
        logger.info("[tableService.__updatePlayJson] ===> Co Du Lieu ");
        biggestWon = playJson.biggestWon;
        if (amountWin > playJson.biggestWon){
            biggestWon = amountWin;
        }
        codeWin = playJson.codeWin;

        if (typeof playJson.bestHand != "undefined") {
            bestHand = playJson.bestHand;
        }else{
            bestHand = cardHands;
        }

        if (typeof playJson.codeWin == "undefined") {
            codeWin = code;
            bestHand = cardHands;
        }

        if (code > playJson.codeWin){
            codeWin = code;
            bestHand = cardHands;
        }
    } else {
        logger.info("[tableService.__updatePlayJson] ===> Khong Co Du Lieu ");
        biggestWon  = amountWin;
        bestHand    = cardHands;
        codeWin     = code;
    }

    let tmpPlayJson = {
        biggestWon: biggestWon,
        bestHand: bestHand,
        codeWin:codeWin
    };

    let arrPro = {
        play_json: JSON.stringify(tmpPlayJson)
    };

    logger.info("[tableService.__updatePlayJson] arrPro: ", arrPro);

    // userDao.updateProperties(uidWinner, arrPro, function (e, _res) {
    pomelo.app.rpc.db.dbRemote.updateProperties('*', uidWinner, arrPro, (e, code, _res) => {
        logger.info("[tableService.__updatePlayJson] Sau khi update thanh cong playJson: e: ", e, " code: ", code, " => _res:", _res);
        cb(null, _res);
    });

};

function checkUidInWinner(uid, winnersList) {
    var tmp = false;
    for(var i=0;i<winnersList.length;i++){
        if (uid === winnersList[i].id){
            tmp = true;
        }
    }
    return tmp;
}

/**
 * Handle end of game or broadcast game state to users
 *
 * @param {string} tid id of the table
 * @param {function} cb callback
 *
 */
TableService.prototype.handleGameState = function(tid, cb){
    var me = this;
    var table = me.tables[tid];
    // check table
    if (table) {
        if (table.table && table.table.game && table.table.game.roundName == 'GameEnd' && table.state == 'IN_PROGRESS' && table.table.active) {
            logger.info("[tableService.handleGameState] table.table.game.roundName: ", table.table.game.roundName, ' -> call endGame');

            // Gửi thông tin kết quả ván chơi trước khi gọi endGame
            me.broadcastEndGameResults(tid);

            // Sau đó mới gọi endGame để xử lý kết thúc ván chơi
            me.endGame(tid, cb);
        } else {
            // logger.info("table.table.player: ", table.table.players);
            var tmpArr = [];
            _.each(table.table.players, function (item) {
                var _item = _.pick(item, "id", "playerName", "chips", "folded", "folded", "allIn", "talked");
                tmpArr.push(_item);
            });

            logger.info("tmpArr: ", tmpArr);

            me.app.get('channelService').getChannel(tid, true).pushMessage({
                route: 'onUpdateUsers',
                action: 'IN_PLAY',
                members: tmpArr //table.table.members
            });
            me.broadcastGameState(tid);
            cb();
        }
    }else{
        cb();
    }
};


/**
 * Broadcast game state by iteratively pushing game details to clients
 *
 * @param {string} tid id
 *
 */
TableService.prototype.broadcastGameState = function(tid) {
    logger.info("[tableService.broadcastGameState] tid: ", tid);
    var i = 0;
    var me = this;
    var channelService = me.app.get('channelService');
    var channel = channelService.getChannel(tid, false);

    function broadcast() {
        if(i == me.tables[tid].table.members.length){
            if(me.tables[tid].state == 'IN_PROGRESS' && me.tables[tid].table.active){
                me.tables[tid].table.startTimer();
            }
            return;
        }
        /*
        //begin: added - check neu player đã đứng dậy hoặc thoát ra thì gọi fold
        logger.info("broadcastGameState===> i = ", i , " => data: ", me.tables[tid].table.players[i]);
        //logger.info("broadcastGameState===> danh sach thoat: ", me.tables[tid].table.playersToRemove);
        var _checkMe = checkCurrentPlayerToRemove(me.tables[tid].table.players[i], me.tables[tid].table.playersToRemove, me.tables[tid].table.players);
        if (_checkMe){
            logger.info("broadcastGameState=> currentPlay is remove out game => call fold action");
            me.tables[tid].table.players[i].Fold();
        }
        //end: added
        */
        // var uid = me.tables[tid].table.members[i].id;
        let uid = 0
        if (me.tables[tid].table.members[i]) {
            uid = me.tables[tid].table.members[i].id;
        } else {
            uid = 0;
        }

        if(channel.getMember(uid)){
            channelService.pushMessageByUids({
                route  : 'onTableEvent',
                msg    : me.getTableJSON(tid, uid),
                timestamp: Math.floor(Date.now() / 1000)
            }, [{
                uid : uid,
                sid : channel.getMember(uid)['sid']
            }], function(){
                ++i;
                broadcast();
            });
        }else{
            ++i;
            broadcast();
        }

        // Không reset gameWinners ở đây nữa, để đảm bảo thông tin người thắng được giữ lại cho event onEndGame

    } // end function broadcast

    broadcast();

};

function checkCurrentPlayerToRemove(player, arrRemove, players) {

    for(var i=0; i<arrRemove.length; i+=1) {
        if (players[arrRemove[i]].id === player.id){
            logger.info("checkCurrentPlayerToRemove===> User thoat: ", players[arrRemove[i]]);
            return true;
        }
    }
    return false;
}


/**
 * Hàm push action turn cuối cùng khi kết thúc game
 * - Để cho các player khác cập nhật được action vừa thực hiện của player cuối cùng
 * @param tid
 */
TableService.prototype.broadcastEndGameState = function(tid) {

    var i               = 0;
    var me              = this;
    var channelService  = me.app.get('channelService');
    var channel         = channelService.getChannel(tid, false);

    // check table ton tai moi thuc hien
    if(me.tables[tid]) {
        if (me.tables[tid].table.members[i] !== undefined) {
            var uid = me.tables[tid].table.members[i].id;

            logger.info("[tableService] >> broadcastEndGameState ", me.getTableJSON(tid, uid));

            if (channel.getMember(uid)) {
                me.app.get('channelService').getChannel(tid, false).pushMessage({
                    //route: consts.GAME.ROUTER.GAME_EVENT, //'onTableEvent',
                    route: consts.GAME.ROUTER.END_TURN,
                    msg: me.getTableJSON(tid, uid)
                });
            }
        }// end check danh sach thanh vien cua ban con co nguoi
    }
};

/**
 * Hàm gửi kết quả cuối cùng của ván chơi đến tất cả người chơi
 * - Gửi thông tin chi tiết về kết quả ván chơi (gameWinners, sidepots, ...)
 * - Được gọi sau broadcastEndGameState để client có thể hiển thị kết quả ván chơi
 * @param {string} tid - Table ID
 */
TableService.prototype.broadcastEndGameResults = function(tid) {
    var me = this;
    var channelService = me.app.get('channelService');
    var channel = channelService.getChannel(tid, false);

    // Kiểm tra bàn tồn tại
    if (!me.tables[tid]) {
        logger.error("[tableService] >> broadcastEndGameResults: Table not found", tid);
        return;
    }

    // Lấy thông tin chi tiết của bàn chơi
    var tableInfo = me.getTableJSON(tid);
    var table = me.tables[tid].table;

    // Xử lý trường hợp đặc biệt: Nếu không có người thắng nhưng có sidepot
    if ((!tableInfo.gameWinners || tableInfo.gameWinners.length === 0) &&
        tableInfo.game.sidepots && tableInfo.game.sidepots.length > 0) {

        logger.info("[tableService] >> broadcastEndGameResults: No winners found but sidepots exist. Adding default winner.");

        // Tìm người chơi còn lại không fold
        var activePlayers = [];
        for (var i = 0; i < table.players.length; i++) {
            if (!table.players[i].folded) {
                activePlayers.push(table.players[i]);
            }
        }

        // Nếu chỉ còn một người chơi không fold, họ là người thắng
        if (activePlayers.length === 1) {
            var winner = activePlayers[0];
            var totalPot = 0;

            // Tính tổng pot từ tất cả sidepots
            for (var j = 0; j < tableInfo.game.sidepots.length; j++) {
                totalPot += tableInfo.game.sidepots[j].amount;
            }

            // Tạo danh sách người chơi cho winner
            var playersList = [];
            for (var k = 0; k < table.players.length; k++) {
                var player = table.players[k];
                var playerInfo = {
                    id: player.id,
                    playerName: player.playerName,
                    actorNr: player.actorNr,
                    cards: player.cards,
                    hand: player.hand,
                    folded: player.folded,
                    allIn: player.allIn,
                    chips: player.chips,
                    oldChips: player.oldChips,
                    amount: player.id === winner.id ? totalPot : -Math.abs(player.oldChips - player.chips),
                    isWinner: player.id === winner.id
                };
                playersList.push(playerInfo);
            }

            // Thêm người thắng vào gameWinners
            tableInfo.gameWinners = [{
                playerName: winner.playerName,
                id: winner.id,
                amount: totalPot,
                potType: "main pot",
                potIndex: 0,
                hand: winner.hand,
                chips: winner.chips,
                players: playersList,
                broad: tableInfo.game.board,
                sidepots: tableInfo.game.sidepots
            }];

            logger.info("[tableService] >> broadcastEndGameResults: Added default winner: " + winner.playerName + " with amount: " + totalPot);
        }
    }

    // Đảm bảo có thông tin gameWinners và sidepots
    if (!tableInfo.gameWinners || tableInfo.gameWinners.length === 0) {
        logger.warn("[tableService] >> broadcastEndGameResults: No winners found for table", tid);
    }

    logger.info("[tableService] >> broadcastEndGameResults: Sending end game results for table", tid);

    // Gửi thông tin kết quả ván chơi đến tất cả người chơi trong bàn
    channel.pushMessage({
        route: consts.GAME.ROUTER.END_GAME,
        msg: tableInfo
    });
};


/**
 * Shuffles an array
 *
 * @param {array} ary an array
 *
 */
TableService.prototype.shuffle = function(ary){
    var currentIndex = ary.length, temporaryValue, randomIndex;
    while(0 !== currentIndex){
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;
        temporaryValue = ary[currentIndex];
        ary[currentIndex] = ary[randomIndex];
        ary[randomIndex] = temporaryValue;
    }
    return ary;
};


TableService.prototype.kickUser = function (uid, cb) {

    var self = this;
    var sessionService = self.app.get('sessionService');
    logger.info("sessionService3333: ", self.app);
    self.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (u) {
        logger.info("[app||servers||game||handler||userHandler => kickUser");
        if (u && !!u.sessionId) {
            logger.info("[handler.kickUser]-> User exist -> Kick User -> ", u);
            sessionService.kickBySessionId(u.sessionId, null);

            self.app.rpc.manager.userRemote.onUserDisconnect(null, {uid: uid}, function () {
                logger.info("kickUser >> onUserDisconnect >> successfully");
            });

            //var _msg = "Bạn bị kick khỏi game";
            //messageService.pushMessageToPlayer(uid, consts.GAME.ROUTER.KICK_USER, {msg: _msg});
        }
        cb(CODE.OK);
    });
};


TableService.prototype.autoSuggestTableHandler = function (cb) {

    var me = this;

    async.waterfall([
        function(callback) {
            var tblList     = me.getTables();
            callback(null, tblList);
        },
        function(tblList, callback) {

            var _data = {
                data: {},
                tid : null
            };
            var _result = {
                code: 404,
                data: _data
            };

            if (tblList.tables.length > 0) {

                var _arrTable = _.filter(tblList.tables, function (num) {
                    return num.members > 0;
                });

                if (_arrTable.length > 0) {
                    var _arrSortByPlayers   = _.sortBy(_arrTable, 'players');
                    var _tblInfo            = _arrSortByPlayers[Math.floor(Math.random() * _arrSortByPlayers.length)];
                    _data                   = {data: _tblInfo, tid: _tblInfo.id};

                    _result.data = _data;
                    _result.code = 200;
                }
            }

            callback(null, _result);
        }
    ], function (err, result) {
        // result now equals 'done'
        cb(result);
    });

};

/**
 * Get all tables in a specific zone
 *
 * @param {String} zone - Zone identifier (TS, TC, CC)
 * @param {Function} cb - Callback function
 */
TableService.prototype.getTablesByZone = function(zone, cb) {
    var tablesInZone = [];

    // Filter tables by zone
    for (var tid in this.tables) {
        if (this.tables[tid].zone === zone) {
            tablesInZone.push({
                id: tid,
                // Include other table info as needed
            });
        }
    }

    cb(null, tablesInZone);
};

/**
 * Get all tables in zones
 *
 * @param {Array} zones - Zone identifier (TS, TC, CC)
 * @param {Function} cb - Callback function
 */
TableService.prototype.getTablesByZones = function(zones, cb) {
    var tablesInZone = [];

    // Filter tables by zone
    for (var tid in this.tables) {
        if (zones.includes(this.tables[tid].zone)) {
            tablesInZone.push({
                id: tid,
            });
        }
    }

    cb(null, tablesInZone);
};

var onUserLeave = function(app, session){
    if(!session || !session.uid || !session.get('tid')){
        logger.info("app||servers||connector||handler||entryHandler|| => onUserLeave (1111)");
        return;
    }

    if(session.get('tid')){
        logger.info("app||servers||connector||handler||entryHandler|| => onUserLeave (2222)");
        app.rpc.chat.chatRemote.disconnect(session, session.uid, function(){});
        app.rpc.game.tableRemote.removeMember(session, session.uid, app.get('serverId'), session.get('tid'), function(){});

        app.rpc.manager.userRemote.onUserDisconnect(null, {roomId: session.settings.roomId, uid: session.uid}, function () {

        });

    }
};