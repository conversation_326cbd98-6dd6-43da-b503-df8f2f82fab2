# SNG Tournament Feature

## Overview

The SNG (Sit and Go) Tournament feature allows players to participate in poker tournaments with a fixed number of players. Unlike regular cash games, SNG tournaments have a fixed buy-in amount, and players compete until one player has all the chips. The tournament ends when only one player remains, and prizes are awarded based on final rankings.

## Key Features

- Fixed buy-in amount with tournament fee
- Fixed number of players (5 or 9)
- Increasing blind levels at regular intervals
- Elimination of players when they run out of chips
- Prize distribution based on final rankings
- Tournament history and statistics

## Database Structure

The SNG tournament feature uses the following database tables:

1. `sng_tournaments` - Stores tournament information
2. `sng_tournament_players` - Stores player participation and status
3. `sng_blind_levels` - Stores blind level information for each tournament
4. `sng_hand_histories` - Stores hand history information for each tournament
5. `sng_rewards` - Stores reward information for tournament winners
6. `sng_tournament_logs` - Stores detailed logs of all tournament actions and events

## Implementation Details

### Tournament States

- `WAITING` - Tournament is waiting for players to register
- `READY` - Tournament has enough players and is ready to start
- `IN_PROGRESS` - Tournament is in progress
- `ENDED` - Tournament has ended

### Player States

- `ACTIVE` - Player is still active in the tournament
- `ELIMINATED` - Player has been eliminated from the tournament
- `WINNER` - Player is the winner of the tournament

### Blind Levels

Blind levels increase at regular intervals (default: 5 minutes). Each level has:

- Small blind amount
- Big blind amount
- Ante amount (optional)
- Duration in seconds

### Prize Distribution

The prize pool is distributed among the top players based on their final rankings:

- 1st place: 50% of prize pool
- 2nd place: 30% of prize pool
- 3rd place: 20% of prize pool

## API Endpoints

### Tournament Management

- `game.sngTournamentHandler.getTournaments` - Get list of available tournaments
- `game.sngTournamentHandler.getTournament` - Get tournament details
- `game.sngTournamentHandler.createTournament` - Create a new tournament (admin only)
- `game.sngTournamentHandler.registerTournament` - Register for a tournament by type and level
- `game.sngTournamentHandler.leaveTournament` - Leave a tournament (automatically detects which tournament the player is registered for)
- `game.sngTournamentHandler.execute` - Execute an action in a tournament game

## Events

- `onSngTournamentList` - List of available tournaments
- `onSngTournamentJoin` - Player joined a tournament
- `onSngTournamentStatus` - Tournament status update
- `onSngBlindUpdate` - Blind level update
- `onSngTournamentResult` - Tournament results
- `onSngPlayerEliminated` - Player eliminated from tournament

## Integration with Existing Game

The SNG tournament feature integrates with the existing poker game by:

1. Using the same table and game logic
2. Adding tournament-specific features like blind increases and player elimination
3. Tracking tournament progress and results
4. Managing tournament rewards

## Configuration

Tournament settings can be configured in:

- `game-server/app/consts/sngConsts.js` - Constants for SNG tournaments
- `game-server/config/data/sngTournaments.json` - Tournament configuration

## Testing

Use the test script `game-server/test/sngTournamentTest.js` to test the SNG tournament functionality.

## Tournament Logging System

The SNG tournament feature includes a comprehensive logging system that tracks all important actions and events during a tournament's lifecycle. This is implemented through the `sng_tournament_logs` table, which stores detailed information about:

- Player registrations
- Tournament status changes
- Blind level increases
- Player eliminations
- Prize distributions
- Tournament completions

Each log entry includes:

- Tournament ID
- Player ID (if applicable)
- Action type
- Detailed data in JSON format
- Amount (if applicable)
- Timestamp

This logging system provides:

1. Complete audit trail of tournament actions
2. Data for analytics and reporting
3. Troubleshooting capabilities for resolving disputes
4. Historical performance metrics

The `logTournamentAction` function in `sngTournamentService.js` handles all logging operations, ensuring consistent and reliable record-keeping across the tournament system.

## Future Improvements

- Multi-table tournaments
- Tournament leaderboards
- Tournament statistics
- Tournament chat
- Tournament spectating
- Enhanced analytics based on tournament logs

## Tóm tắt các cải tiến

Tôi đã thực hiện các cải tiến sau đây để tăng cường chức năng tính toán thắng thua, cập nhật thứ hạng và chia thưởng trong giải đấu SNG:

### 1. Cải thiện xử lý loại bỏ người chơi:

- Kiểm tra người chơi đã bị loại trước đó chưa
- Xác định người chiến thắng khi chỉ còn một người chơi
- Ghi log chi tiết hơn về quá trình loại bỏ người chơi

### 2. Thêm phương thức xử lý kết thúc giải đấu:

- Thêm phương thức handleTournamentEnd để xử lý khi có người chiến thắng
- Cập nhật trạng thái người chiến thắng và kết thúc giải đấu

### 3. Cải thiện phân phối giải thưởng:

- Điều chỉnh tỷ lệ phân phối dựa trên số lượng người chơi
- Xử lý đúng các trường hợp đặc biệt (1 hoặc 2 người chơi)
- Ghi log chi tiết về quá trình phân phối giải thưởng

### 4. Cải thiện thông báo kết quả giải đấu:

- Lấy thông tin giải thưởng từ cơ sở dữ liệu
- Tính toán giải thưởng dự phòng nếu chưa có trong cơ sở dữ liệu
- Ghi log chi tiết về thông báo kết quả

### 5. Cải thiện xử lý thứ hạng người chơi:

- Sắp xếp người chơi theo trạng thái (ACTIVE trước, ELIMINATED sau)
- Tính toán thứ hạng dựa trên số người chơi còn hoạt động
- Ghi log chi tiết về thứ hạng người chơi

### 6. Cải thiện kết thúc giải đấu:

- Thu thập thống kê chi tiết về giải đấu
- Ghi log chi tiết về quá trình kết thúc giải đấu
- Xử lý dọn dẹp tài nguyên giải đấu

### 7. Cải thiện thông báo loại bỏ người chơi:

- Thêm thông tin về số người chơi còn lại
- Thêm thông tin về mức blind hiện tại
- Ghi log chi tiết về thông báo loại bỏ người chơi

### 8. Cải thiện xử lý tăng mức blind:

- Lưu trữ mức blind hiện tại trong bộ nhớ
- Kiểm tra số người chơi còn hoạt động trước khi tăng mức blind
- Ghi log chi tiết về quá trình tăng mức blind

### 9. Cải thiện lấy mức blind hiện tại:

- Lấy mức blind từ bộ nhớ nếu có
- Mặc định về mức 1 nếu không tìm thấy

### 10. Cải thiện thông báo tăng mức blind:

- Thêm thông tin về số người chơi còn hoạt động
- Thêm thông tin về thời gian tăng mức blind tiếp theo
- Ghi log chi tiết về thông báo tăng mức blind

### 11. Thêm hệ thống ghi log chi tiết cho giải đấu:

- Tạo bảng `sng_tournament_logs` để lưu trữ chi tiết các hành động trong giải đấu
- Cập nhật hàm `logTournamentAction` để ghi log vào bảng mới
- Lưu trữ đầy đủ thông tin về người chơi, loại hành động, dữ liệu chi tiết và thời gian
- Hỗ trợ truy vấn và phân tích dữ liệu giải đấu
- Tạo cơ sở dữ liệu cho việc giải quyết tranh chấp và báo cáo thống kê

### 12. Cải thiện logic đăng ký và tham gia giải đấu:

- **Gán vị trí ngồi ngay khi đăng ký**: Người chơi được gán `seat_number` ngay lập tức theo thứ tự đăng ký (0, 1, 2, ...)
- **Tạo bàn chờ ngay lập tức**: Khi có người đăng ký đầu tiên, hệ thống tự động tạo bàn chờ và cho người chơi ngồi xuống
- **Tự động tạo giải đấu mới**: Khi không tìm thấy giải đấu phù hợp, hệ thống tự động tạo giải đấu mới ngay lập tức
- **Chuyển đổi bàn chờ thành bàn chơi**: Khi đủ người, bàn chờ được chuyển thành bàn chơi và tự động bắt đầu game
- **Cải thiện trải nghiệm người chơi**: Người chơi có thể thấy bàn chơi và các người chơi khác ngay từ khi đăng ký

### 13. Sửa lỗi tạo giải đấu tự động:

- **Sửa lỗi "No available tournament found after creation attempt"**: Thay đổi logic tạo giải đấu từ async sang sync để đảm bảo giải đấu được tạo thành công
- **Cải thiện logic tìm kiếm giải đấu**: Khi không tìm thấy giải đấu, hệ thống tạo ngay lập tức thay vì gọi hàm kiểm tra phức tạp
- **Đảm bảo bàn tự động start**: Sửa logic để bàn tự động bắt đầu game khi đủ số lượng người chơi

Các cải tiến này đảm bảo rằng chức năng tính toán thắng thua, cập nhật thứ hạng và chia thưởng trong giải đấu SNG hoạt động chính xác và đáng tin cậy, đồng thời không ảnh hưởng đến luồng chơi tự do hiện có. Hệ thống ghi log chi tiết cung cấp khả năng theo dõi và phân tích toàn diện cho các hoạt động của giải đấu.

## Logic tiền ảo và vị trí ngồi

### Vị trí ngồi (seat_number)

- **Khi đăng ký**: `seat_number` được gán ngay lập tức theo thứ tự đăng ký (0, 1, 2, ...)
- **Khi tournament bắt đầu**: Sử dụng vị trí đã được gán từ khi đăng ký
- **Lý do**: Người chơi được ngồi xuống bàn chờ ngay khi đăng ký để có trải nghiệm tốt hơn

### Hệ thống tiền ảo

- **Tiền thật**: `player.balance` - bị trừ buy_in + fee khi đăng ký
- **Tiền ảo**: `current_chips` - 100M chips để chơi trong tournament
- **Tách biệt hoàn toàn**: Tiền ảo không ảnh hưởng tiền thật
- **An toàn**: Chỉ giải thưởng mới được cộng vào tiền thật

### Luồng tiền chi tiết

1. **Đăng ký**: Trừ tiền thật (buy_in + fee), cấp 100M chips ảo, gán vị trí ngồi và tạo bàn chờ
2. **Chơi**: Sử dụng chips ảo, tiền thật không đổi
3. **Kết thúc**: Giải thưởng từ reward_pool cộng vào tiền thật

## Cải tiến Push Message và Channel Management

### Sửa lỗi push message format và channel management

- **Sửa lỗi push message format**: Thay thế `channel.pushMessage()` bằng `channelService.pushMessageByUids()` để đảm bảo format đúng
- **Thêm helper functions**: `getChannelReceivers()` và `sendPushMessage()` để tối ưu code và giảm trùng lặp
- **Cải thiện channel management**: Thêm function `ensurePlayersInChannel()` để đảm bảo tất cả người chơi luôn ở trong tournament channel
- **Sửa lỗi "No receivers found"**: Đảm bảo người chơi được thêm vào channel khi đăng ký và khi tournament bắt đầu
- **Cải thiện registration handler**: Sửa lỗi context binding và thêm verification cho channel membership
- **Cải thiện logging**: Thêm logging chi tiết cho việc thêm/xóa người chơi khỏi channels
- **Đảm bảo format đúng**: Tất cả SNG Tournament events được gửi với format: `groups: {"connector-server-3":[561]}`
- **Sửa lỗi cho các events**: SNG_TOURNAMENT_JOIN, SNG_TOURNAMENT_STATUS, SNG_TOURNAMENT_BLIND_UPDATE, SNG_TOURNAMENT_PLAYER_ELIMINATED, SNG_TOURNAMENT_RESULT
- **Tối ưu hóa code**: Sử dụng helper functions thay vì lặp lại logic push message

### Kết quả cải tiến

Việc sửa lỗi push message format và cải thiện channel management đảm bảo rằng:

- **Thông báo được gửi đúng**: Tất cả SNG Tournament events được gửi với format chính xác và nhất quán với hệ thống bàn chơi thường
- **Không còn lỗi "No receivers found"**: Người chơi luôn được đảm bảo ở trong tournament channel khi cần thiết
- **Trải nghiệm người dùng tốt hơn**: Người chơi nhận được thông báo kịp thời về các sự kiện trong tournament
- **Hệ thống ổn định hơn**: Giảm thiểu lỗi và cải thiện độ tin cậy của hệ thống thông báo
- **Code sạch hơn**: Sử dụng helper functions giúp code dễ bảo trì và mở rộng

## Cải tiến xử lý người chơi offline và luồng chơi

### Sửa lỗi Server ID và Channel Management

- **Sửa lỗi Server ID**: Thay thế việc sử dụng `self.app.getServerId()` bằng việc lấy `serverId` thực tế từ `userCached.serverId` của từng người chơi
- **Kiểm tra trạng thái online**: Chỉ thêm người chơi vào channel nếu họ đang online (có trong userCached)
- **Bỏ qua người chơi offline**: Người chơi offline sẽ không được thêm vào channel để tránh lỗi push message
- **Logging chi tiết**: Thêm logging để theo dõi số lượng người chơi online/offline và quá trình thêm vào channel

### Cải thiện luồng chơi với người chơi offline

- **Game vẫn bắt đầu**: Tournament vẫn bắt đầu kể cả khi có người chơi offline, đảm bảo trải nghiệm cho người chơi online
- **Auto-fold người chơi offline**: Khi đến lượt của người chơi offline, hệ thống sẽ tự động fold ngay lập tức thay vì chờ timeout
- **Override startTimer**: Tùy chỉnh hàm `startTimer` của table để kiểm tra trạng thái online và xử lý accordingly
- **Tiếp tục game logic**: Game tiếp tục diễn ra bình thường với người chơi online, người chơi offline sẽ dần bị loại

### Kết quả cải tiến offline handling

- **Luồng chơi mượt mà**: Game không bị gián đoạn bởi người chơi offline
- **Trải nghiệm tốt hơn**: Người chơi online không phải chờ đợi người chơi offline
- **Logic game chính xác**: Người chơi offline vẫn tham gia tournament nhưng sẽ tự động fold và dần bị loại
- **Tính công bằng**: Tất cả người chơi đã đăng ký đều tham gia tournament, chỉ khác ở cách xử lý turn
- **Hệ thống ổn định**: Không có lỗi do thiếu người chơi hoặc channel management sai
